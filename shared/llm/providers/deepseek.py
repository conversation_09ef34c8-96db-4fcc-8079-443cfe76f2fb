"""
DeepSeek LLM提供商实现 - Shared模块
整合了最佳实践的DeepSeek LLM实现
"""

import logging
from typing import List, Optional
import time
import asyncio

try:
    from langchain_deepseek import ChatDeepSeek
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False
    ChatDeepSeek = None

from ..base import BaseLLM, LLMResponse, LLMProvider
from ..base import (
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
)
from ..factory import register_llm_provider

logger = logging.getLogger(__name__)


@register_llm_provider(LLMProvider.DEEPSEEK)
class DeepSeekLLM(BaseLLM):
    """DeepSeek LLM实现 - 统一版本"""

    def _initialize_client(self):
        """初始化DeepSeek客户端"""
        if not DEEPSEEK_AVAILABLE:
            raise LLMError(
                "DeepSeek库未安装，请运行: pip install langchain-deepseek"
            )

        try:
            if not self.config.api_key:
                raise LLMAuthenticationError("DeepSeek API密钥未设置")

            # 构建客户端参数
            client_params = {
                "api_key": self.config.api_key,
                "model": self.config.model,
                "temperature": self.config.temperature,
                "timeout": self.config.timeout,
            }

            # 设置base_url
            if self.config.base_url:
                client_params["base_url"] = self.config.base_url

            # 设置max_tokens
            if self.config.max_tokens:
                client_params["max_tokens"] = self.config.max_tokens

            # 添加额外参数
            client_params.update(self.config.extra_params)

            self._client = ChatDeepSeek(**client_params)
            
            self._logger.debug(f"DeepSeek客户端初始化成功: {self.config.model}")
            self._logger.debug(f"配置参数: API密钥={'已设置' if self.config.api_key else '未设置'}, "
                             f"base_url={self.config.base_url}")

        except Exception as e:
            error_msg = f"DeepSeek客户端初始化失败: {str(e)}"
            self._logger.error(error_msg)
            raise LLMConnectionError(error_msg) from e

    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用DeepSeek生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLM响应
        """
        if not self._client:
            raise LLMError("DeepSeek客户端未初始化")

        # 合并配置参数和调用参数
        call_params = self._merge_call_params(**kwargs)
        
        # 移除不支持的参数
        call_params = self._filter_call_params(call_params)

        # 重试机制
        last_exception = None
        for attempt in range(self.config.max_retries):
            try:
                self._logger.debug(
                    f"DeepSeek调用尝试 {attempt + 1}/{self.config.max_retries}"
                )

                # 调用DeepSeek
                response = self._client.invoke(prompt, **call_params)

                # 提取响应内容并处理 None 值
                content = response.content
                if content is None:
                    content = ""
                    self._logger.warning("DeepSeek返回的content为None，设置为空字符串")

                # 记录详细的响应信息用于调试
                self._logger.debug(f"DeepSeek响应详情: content长度={len(content)}")

                # 构建响应对象
                llm_response = LLMResponse(
                    content=content,
                    model=self.config.model,
                    raw_response=response,
                )

                # 尝试提取使用信息
                if hasattr(response, "usage_metadata"):
                    llm_response.usage = response.usage_metadata
                elif hasattr(response, "response_metadata"):
                    llm_response.usage = response.response_metadata.get("usage", {})

                # 提取完成原因
                if hasattr(response, "response_metadata"):
                    metadata = response.response_metadata
                    llm_response.finish_reason = metadata.get("finish_reason")

                self._logger.info(f"DeepSeek调用成功，响应长度: {len(content)}")
                return llm_response

            except Exception as e:
                last_exception = e
                self._logger.warning(f"DeepSeek调用失败 (尝试 {attempt + 1}): {str(e)}")

                # 根据错误类型决定是否重试
                error_str = str(e).lower()
                
                if "rate limit" in error_str or "too many requests" in error_str:
                    if attempt < self.config.max_retries - 1:
                        wait_time = 2**attempt  # 指数退避
                        self._logger.info(f"遇到速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise LLMRateLimitError(f"DeepSeek速率限制: {str(e)}") from e
                        
                elif "authentication" in error_str or "api key" in error_str or "unauthorized" in error_str:
                    raise LLMAuthenticationError(f"DeepSeek认证失败: {str(e)}") from e
                    
                elif "invalid" in error_str or "bad request" in error_str:
                    raise LLMInvalidRequestError(f"DeepSeek请求无效: {str(e)}") from e
                    
                elif attempt < self.config.max_retries - 1:
                    # 其他错误也重试
                    time.sleep(1)
                    continue
                else:
                    break

        # 所有重试都失败了
        raise LLMError(
            f"DeepSeek调用失败，已重试 {self.config.max_retries} 次: {str(last_exception)}"
        ) from last_exception

    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用DeepSeek

        Args:
            prompts: 提示列表
            **kwargs: 额外参数

        Returns:
            响应列表
        """
        responses = []
        total = len(prompts)
        
        for i, prompt in enumerate(prompts):
            try:
                self._logger.debug(f"批量调用DeepSeek: {i+1}/{total}")
                response = self.invoke(prompt, **kwargs)
                responses.append(response)
                
                # 批量调用间隔，避免速率限制
                if i < total - 1:  # 不是最后一个
                    time.sleep(0.1)
                    
            except Exception as e:
                self._logger.error(f"批量调用第 {i+1} 个提示失败: {str(e)}")
                # 创建错误响应
                error_response = LLMResponse(
                    content=f"调用失败: {str(e)}", 
                    model=self.config.model,
                    finish_reason="error"
                )
                responses.append(error_response)

        return responses

    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用DeepSeek

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Yields:
            流式响应片段
        """
        if not self._client:
            raise LLMError("DeepSeek客户端未初始化")

        try:
            # 合并配置参数和调用参数
            call_params = self._merge_call_params(**kwargs)
            call_params = self._filter_call_params(call_params)

            self._logger.debug("开始DeepSeek流式调用")

            # DeepSeek流式调用
            for chunk in self._client.stream(prompt, **call_params):
                if hasattr(chunk, "content") and chunk.content:
                    yield chunk.content

        except Exception as e:
            error_msg = f"DeepSeek流式调用失败: {str(e)}"
            self._logger.error(error_msg)
            raise LLMError(error_msg) from e

    def _filter_call_params(self, params: dict) -> dict:
        """
        过滤调用参数，移除DeepSeek不支持的参数
        
        Args:
            params: 原始参数
            
        Returns:
            过滤后的参数
        """
        # DeepSeek支持的参数
        supported_params = {
            'temperature', 'max_tokens', 'top_p', 'top_k', 
            'stop', 'stream', 'presence_penalty', 'frequency_penalty'
        }
        
        filtered = {k: v for k, v in params.items() if k in supported_params}
        
        # 记录被过滤的参数
        filtered_out = set(params.keys()) - set(filtered.keys())
        if filtered_out:
            self._logger.debug(f"过滤掉不支持的参数: {filtered_out}")
            
        return filtered

    async def async_invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        异步调用DeepSeek（如果支持的话）
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLM响应
        """
        # 目前通过线程池实现异步
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.invoke, prompt, **kwargs)

    def health_check(self) -> bool:
        """
        健康检查 - 重写基类方法以提供更好的检查
        
        Returns:
            是否健康
        """
        try:
            # 使用更适合的参数进行健康检查
            response = self.invoke("测试", max_tokens=10)
            # 检查响应是否存在以及API调用是否成功
            # 对于健康检查，API调用成功比响应内容更重要
            is_healthy = response is not None
            
            if is_healthy:
                self._logger.info(f"DeepSeek健康检查通过: 响应长度={len(response.content) if response.content else 0}")
            else:
                self._logger.warning("DeepSeek健康检查失败: 无响应")
                
            return is_healthy
        except LLMAuthenticationError:
            self._logger.error("DeepSeek认证失败，请检查API密钥")
            return False
        except LLMConnectionError:
            self._logger.error("DeepSeek连接失败，请检查网络和服务状态")
            return False
        except Exception as e:
            self._logger.warning(f"DeepSeek健康检查失败: {e}")
            return False

    def get_supported_models(self) -> List[str]:
        """获取支持的模型列表"""
        return [
            "deepseek-chat", 
            "deepseek-reasoner",
            "deepseek-coder",
            "deepseek-v2.5",
        ]

    def estimate_cost(self, prompt: str, response: Optional[str] = None) -> dict:
        """
        估算API调用成本
        
        Args:
            prompt: 输入提示
            response: 响应内容
            
        Returns:
            成本估算信息
        """
        # 简单的token估算
        input_tokens = len(prompt) // 2  # 粗略估算
        output_tokens = len(response) // 2 if response else 0
        
        # DeepSeek定价（示例，实际价格可能不同）
        input_cost_per_1k = 0.0014  # USD per 1K tokens
        output_cost_per_1k = 0.0028  # USD per 1K tokens
        
        input_cost = (input_tokens / 1000) * input_cost_per_1k
        output_cost = (output_tokens / 1000) * output_cost_per_1k
        total_cost = input_cost + output_cost
        
        return {
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_tokens': input_tokens + output_tokens,
            'input_cost_usd': input_cost,
            'output_cost_usd': output_cost,
            'total_cost_usd': total_cost,
            'model': self.config.model
        } 