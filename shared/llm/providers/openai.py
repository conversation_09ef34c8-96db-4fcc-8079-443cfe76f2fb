"""
OpenAI LLM提供商实现 - Shared模块
整合了最佳实践的OpenAI LLM实现
"""

import logging
from typing import List, Any, Dict, Optional
import time
import asyncio

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

from ..base import BaseLLM, LLMResponse, LLMProvider
from ..base import (
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
)
from ..factory import register_llm_provider

logger = logging.getLogger(__name__)


@register_llm_provider(LLMProvider.OPENAI)
class OpenAILLM(BaseLLM):
    """OpenAI LLM实现 - 统一版本"""

    def _initialize_client(self):
        """初始化OpenAI客户端"""
        if not OPENAI_AVAILABLE:
            raise LLMError("OpenAI库未安装，请运行: pip install openai")

        try:
            if not self.config.api_key:
                raise LLMAuthenticationError("OpenAI API密钥未设置")

            client_params = {
                "api_key": self.config.api_key,
                "timeout": self.config.timeout,
            }

            # 如果设置了自定义base_url，使用它（支持兼容OpenAI格式的其他服务）
            if self.config.base_url:
                client_params["base_url"] = self.config.base_url

            # 添加额外参数
            client_params.update(self.config.extra_params)

            self._client = OpenAI(**client_params)
            
            self._logger.debug(f"OpenAI客户端初始化成功: {self.config.model}")
            self._logger.debug(f"配置参数: API密钥={'已设置' if self.config.api_key else '未设置'}, "
                             f"base_url={self.config.base_url}")

        except Exception as e:
            error_msg = f"OpenAI客户端初始化失败: {str(e)}"
            self._logger.error(error_msg)
            raise LLMConnectionError(error_msg) from e

    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用OpenAI生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLM响应
        """
        if not self._client:
            raise LLMError("OpenAI客户端未初始化")

        # 构建消息
        messages = self._build_messages(prompt, kwargs.get('context'))

        # 合并配置参数和调用参数
        call_params = {
            "model": self.config.model,
            "messages": messages,
            **self._merge_call_params(**kwargs),
        }

        # 移除不支持的参数
        call_params = self._filter_call_params(call_params)

        # 记录调用参数用于调试
        self._logger.info(f"OpenAI调用参数: {call_params}")
        self._logger.info(f"提示词长度: {len(prompt)}")
        self._logger.debug(f"提示词前100字符: {prompt[:100]}")

        # 特别检查 max_tokens 设置
        if 'max_tokens' in call_params:
            self._logger.warning(f"检测到 max_tokens 限制: {call_params['max_tokens']}")
        else:
            self._logger.info("未设置 max_tokens 限制")

        # 重试机制
        last_exception = None
        for attempt in range(self.config.max_retries):
            try:
                self._logger.debug(f"OpenAI调用尝试 {attempt + 1}/{self.config.max_retries}")

                # 调用OpenAI
                response = self._client.chat.completions.create(**call_params)

                # 记录原始响应用于调试
                self._logger.debug(f"OpenAI原始响应: {response}")
                self._logger.debug(f"响应类型: {type(response)}")

                # 检查响应结构
                if not response.choices:
                    self._logger.error("OpenAI响应中没有choices")
                    raise LLMError("OpenAI响应格式错误：缺少choices")

                choice = response.choices[0]
                self._logger.debug(f"第一个choice: {choice}")
                self._logger.debug(f"choice.message: {choice.message}")

                # 提取响应内容
                content = choice.message.content

                # 记录详细的响应信息用于调试
                self._logger.info(f"OpenAI响应详情: "
                                f"choices数量={len(response.choices)}, "
                                f"finish_reason={choice.finish_reason}, "
                                f"content类型={type(content)}, "
                                f"content值={repr(content)}, "
                                f"content长度={len(content) if content else 0}")

                # 处理 None 值的情况
                if content is None:
                    content = ""
                    self._logger.warning("OpenAI返回的content为None，设置为空字符串")

                # 构建响应对象
                llm_response = LLMResponse(
                    content=content,
                    model=response.model,
                    finish_reason=response.choices[0].finish_reason,
                    raw_response=response,
                )

                # 提取使用信息
                if response.usage:
                    llm_response.usage = {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens,
                    }

                self._logger.info(f"OpenAI调用成功，响应长度: {len(content)}, "
                                f"完成原因: {response.choices[0].finish_reason}")
                return llm_response

            except Exception as e:
                last_exception = e
                self._logger.warning(f"OpenAI调用失败 (尝试 {attempt + 1}): {str(e)}")

                # 根据错误类型决定是否重试
                error_str = str(e).lower()
                
                if "rate limit" in error_str or "429" in error_str or "too many requests" in error_str:
                    if attempt < self.config.max_retries - 1:
                        wait_time = 2**attempt  # 指数退避
                        self._logger.info(f"遇到速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise LLMRateLimitError(f"OpenAI速率限制: {str(e)}") from e
                        
                elif "authentication" in error_str or "401" in error_str or "unauthorized" in error_str:
                    raise LLMAuthenticationError(f"OpenAI认证失败: {str(e)}") from e
                    
                elif "invalid" in error_str or "400" in error_str or "bad request" in error_str:
                    raise LLMInvalidRequestError(f"OpenAI请求无效: {str(e)}") from e
                    
                elif attempt < self.config.max_retries - 1:
                    # 其他错误也重试
                    time.sleep(1)
                    continue
                else:
                    break

        # 所有重试都失败了
        raise LLMError(
            f"OpenAI调用失败，已重试 {self.config.max_retries} 次: {str(last_exception)}"
        ) from last_exception

    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用OpenAI

        Args:
            prompts: 提示列表
            **kwargs: 额外参数

        Returns:
            响应列表
        """
        responses = []
        total = len(prompts)
        
        for i, prompt in enumerate(prompts):
            try:
                self._logger.debug(f"批量调用OpenAI: {i+1}/{total}")
                response = self.invoke(prompt, **kwargs)
                responses.append(response)
                
                # 批量调用间隔，避免速率限制
                if i < total - 1:  # 不是最后一个
                    time.sleep(0.1)
                    
            except Exception as e:
                self._logger.error(f"批量调用第 {i+1} 个提示失败: {str(e)}")
                # 创建错误响应
                error_response = LLMResponse(
                    content=f"调用失败: {str(e)}", 
                    model=self.config.model,
                    finish_reason="error"
                )
                responses.append(error_response)

        return responses

    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用OpenAI

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Yields:
            流式响应片段
        """
        if not self._client:
            raise LLMError("OpenAI客户端未初始化")

        try:
            # 构建消息
            messages = self._build_messages(prompt, kwargs.get('context'))

            # 合并配置参数和调用参数
            call_params = {
                "model": self.config.model,
                "messages": messages,
                "stream": True,
                **self._merge_call_params(**kwargs),
            }

            # 移除不支持的参数
            call_params = self._filter_call_params(call_params)

            self._logger.debug("开始OpenAI流式调用")

            # OpenAI流式调用
            stream = self._client.chat.completions.create(**call_params)
            
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            error_msg = f"OpenAI流式调用失败: {str(e)}"
            self._logger.error(error_msg)
            raise LLMError(error_msg) from e

    def _build_messages(self, prompt: str, context: Optional[str] = None) -> List[Dict[str, str]]:
        """
        构建消息列表
        
        Args:
            prompt: 用户提示
            context: 上下文信息
            
        Returns:
            消息列表
        """
        messages = []
        
        # 添加系统消息（如果有上下文）
        if context:
            messages.append({"role": "system", "content": context})
        
        # 添加用户消息
        messages.append({"role": "user", "content": prompt})
        
        return messages

    def _filter_call_params(self, params: dict) -> dict:
        """
        过滤调用参数，移除OpenAI不支持的参数
        
        Args:
            params: 原始参数
            
        Returns:
            过滤后的参数
        """
        # OpenAI支持的参数
        supported_params = {
            'model', 'messages', 'temperature', 'max_tokens', 'top_p',
            'frequency_penalty', 'presence_penalty', 'stop', 'stream',
            'logit_bias', 'user', 'response_format', 'seed', 'tools',
            'tool_choice', 'parallel_tool_calls'
        }
        
        filtered = {k: v for k, v in params.items() if k in supported_params}
        
        # 记录被过滤的参数
        filtered_out = set(params.keys()) - set(filtered.keys())
        if filtered_out:
            self._logger.debug(f"过滤掉不支持的参数: {filtered_out}")
            
        return filtered

    async def async_invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        异步调用OpenAI
        
        Args:
            prompt: 输入提示
            **kwargs: 额外参数
            
        Returns:
            LLM响应
        """
        # 目前通过线程池实现异步
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.invoke, prompt, **kwargs)

    def health_check(self) -> bool:
        """
        健康检查 - 重写基类方法以提供更好的检查
        
        Returns:
            是否健康
        """
        try:
            # 使用更适合的参数进行健康检查
            response = self.invoke("Test", max_tokens=10)
            # 检查响应是否存在以及API调用是否成功
            # 对于健康检查，API调用成功比响应内容更重要
            is_healthy = response is not None
            
            if is_healthy:
                self._logger.info(f"OpenAI健康检查通过: 响应长度={len(response.content) if response.content else 0}")
            else:
                self._logger.warning("OpenAI健康检查失败: 无响应")
                
            return is_healthy
        except LLMAuthenticationError:
            self._logger.error("OpenAI认证失败，请检查API密钥")
            return False
        except LLMConnectionError:
            self._logger.error("OpenAI连接失败，请检查网络和服务状态")
            return False
        except Exception as e:
            self._logger.warning(f"OpenAI健康检查失败: {e}")
            return False

    def get_supported_models(self) -> List[str]:
        """获取支持的模型列表"""
        return [
            "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
            "gpt-3.5-turbo", "gpt-3.5-turbo-16k",
            "gpt-4-vision-preview", "gpt-4-turbo-preview"
        ]

    def estimate_cost(self, prompt: str, response: Optional[str] = None) -> dict:
        """
        估算API调用成本
        
        Args:
            prompt: 输入提示
            response: 响应内容
            
        Returns:
            成本估算信息
        """
        # 简单的token估算
        input_tokens = len(prompt) // 4  # 粗略估算
        output_tokens = len(response) // 4 if response else 0
        
        # OpenAI定价（示例，根据实际模型调整）
        model_pricing = {
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
        }
        
        pricing = model_pricing.get(self.config.model, {"input": 0.01, "output": 0.03})
        
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]
        total_cost = input_cost + output_cost
        
        return {
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_tokens': input_tokens + output_tokens,
            'input_cost_usd': input_cost,
            'output_cost_usd': output_cost,
            'total_cost_usd': total_cost,
            'model': self.config.model
        }

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model': self.config.model,
            'provider': 'OpenAI',
            'max_tokens': 4096,  # 默认值，实际根据模型不同
            'supports_streaming': True,
            'supports_tools': True,
            'context_window': self._get_context_window()
        }

    def _get_context_window(self) -> int:
        """获取模型的上下文窗口大小"""
        context_windows = {
            "gpt-4": 8192,
            "gpt-4-turbo": 128000,
            "gpt-4o": 128000,
            "gpt-3.5-turbo": 4096,
            "gpt-3.5-turbo-16k": 16384,
        }
        return context_windows.get(self.config.model, 4096) 