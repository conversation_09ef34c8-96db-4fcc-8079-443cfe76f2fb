"""
LLM工厂类 - Shared模块
统一创建和管理不同的LLM提供商，支持灵活的配置传递
"""

import logging
from typing import Dict, Type, Optional, Any, Union, List
from .base import BaseLLM, LLMConfig, LLMProvider, LLMError

logger = logging.getLogger(__name__)


class LLMFactory:
    """LLM工厂类 - 统一管理所有LLM提供商"""
    
    _providers: Dict[LLMProvider, Type[BaseLLM]] = {}
    _instances: Dict[str, BaseLLM] = {}

    @classmethod
    def register_provider(cls, provider: LLMProvider, llm_class: Type[BaseLLM]):
        """
        注册LLM提供商
        
        Args:
            provider: 提供商类型
            llm_class: LLM实现类
        """
        cls._providers[provider] = llm_class
        logger.info(f"注册LLM提供商: {provider.value}")

    @classmethod
    def create_llm(cls, config: LLMConfig, instance_key: Optional[str] = None) -> BaseLLM:
        """
        创建LLM实例
        
        Args:
            config: LLM配置
            instance_key: 实例缓存键，如果提供则会缓存实例
            
        Returns:
            LLM实例
            
        Raises:
            LLMError: 当提供商未注册或创建失败时
        """
        # 检查是否有缓存的实例
        if instance_key and instance_key in cls._instances:
            logger.debug(f"使用缓存的LLM实例: {instance_key}")
            cached_instance = cls._instances[instance_key]
            
            # 验证缓存实例是否与当前配置匹配
            if (cached_instance.config.provider == config.provider and 
                cached_instance.config.model == config.model):
                return cached_instance
            else:
                logger.info(f"配置已变更，移除缓存实例: {instance_key}")
                del cls._instances[instance_key]

        # 检查提供商是否已注册
        if config.provider not in cls._providers:
            available_providers = list(cls._providers.keys())
            raise LLMError(
                f"未注册的LLM提供商: {config.provider.value}. "
                f"可用提供商: {[p.value for p in available_providers]}"
            )

        try:
            # 创建LLM实例
            llm_class = cls._providers[config.provider]
            llm_instance = llm_class(config)
            
            # 缓存实例（如果提供了缓存键）
            if instance_key:
                cls._instances[instance_key] = llm_instance
                logger.debug(f"缓存LLM实例: {instance_key}")
            
            logger.info(f"成功创建LLM实例: {config.provider.value} - {config.model}")
            return llm_instance
            
        except Exception as e:
            logger.error(f"创建LLM实例失败: {config.provider.value} - {str(e)}")
            raise LLMError(f"创建LLM实例失败: {str(e)}") from e

    @classmethod
    def get_available_providers(cls) -> list[LLMProvider]:
        """获取可用的提供商列表"""
        return list(cls._providers.keys())

    @classmethod
    def clear_cache(cls):
        """清空实例缓存"""
        cls._instances.clear()
        logger.info("已清空LLM实例缓存")

    @classmethod
    def remove_cached_instance(cls, instance_key: str):
        """移除指定的缓存实例"""
        if instance_key in cls._instances:
            del cls._instances[instance_key]
            logger.debug(f"移除缓存实例: {instance_key}")

    @classmethod
    def get_instance_info(cls) -> Dict[str, str]:
        """获取实例信息用于调试"""
        return {
            key: f"{instance.get_provider().value}:{instance.get_model()}"
            for key, instance in cls._instances.items()
        }


class ConfigBuilder:
    """配置构建器 - 减少重复代码并修复配置传递问题"""

    @staticmethod
    def build_config_from_settings(settings: Any, provider: LLMProvider) -> LLMConfig:
        """
        从settings构建LLMConfig - 支持多种配置格式
        
        Args:
            settings: 配置对象（支持属性和字典访问）
            provider: LLM提供商
            
        Returns:
            LLM配置实例
        """
        def get_setting(key: str, default: Any = None) -> Any:
            """灵活获取配置值 - 支持大小写和下划线变体"""
            # 尝试多种命名方式
            keys_to_try = [
                key,  # 原始键名
                key.upper(),  # 大写
                key.lower(),  # 小写
                key.replace('_', '').upper(),  # 去下划线大写
                key.replace('_', '').lower(),  # 去下划线小写
            ]
            
            for try_key in keys_to_try:
                # 尝试属性访问
                if hasattr(settings, try_key):
                    value = getattr(settings, try_key)
                    if value is not None:
                        return value
                
                # 尝试字典访问
                if hasattr(settings, 'get') and callable(getattr(settings, 'get')):
                    value = settings.get(try_key)
                    if value is not None:
                        return value
                
                # 尝试直接索引访问
                try:
                    if hasattr(settings, '__getitem__'):
                        value = settings[try_key]
                        if value is not None:
                            return value
                except (KeyError, TypeError):
                    continue
            
            return default

        # 通用配置
        common_config = {
            'provider': provider,
            'temperature': get_setting('temperature', 0.1),
            'max_tokens': get_setting('max_tokens', 4000),
            'timeout': get_setting('timeout', 60),
            'max_retries': get_setting('max_retries', 3),
        }

        # 提供商特定配置
        if provider == LLMProvider.DEEPSEEK:
            api_key = get_setting('deepseek_api_key') or get_setting('DEEPSEEK_API_KEY')
            base_url = get_setting('deepseek_base_url') or get_setting('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
            model = get_setting('deepseek_model') or get_setting('DEEPSEEK_MODEL', 'deepseek-reasoner')
            
            return LLMConfig(
                api_key=api_key,
                base_url=base_url,
                model=model,
                **common_config
            )
            
        elif provider == LLMProvider.OPENAI:
            api_key = get_setting('openai_api_key') or get_setting('OPENAI_API_KEY')
            base_url = get_setting('openai_base_url') or get_setting('OPENAI_BASE_URL')
            model = get_setting('openai_model') or get_setting('OPENAI_MODEL', 'gpt-3.5-turbo')
            
            return LLMConfig(
                api_key=api_key,
                base_url=base_url,
                model=model,
                **common_config
            )
            
        else:
            raise LLMError(f"暂不支持从配置创建提供商: {provider.value}")

    @staticmethod
    def validate_config(config: LLMConfig) -> bool:
        """
        验证配置的完整性
        
        Args:
            config: LLM配置
            
        Returns:
            是否有效
        """
        if not config.api_key:
            logger.error(f"API密钥未设置: {config.provider.value}")
            return False
            
        if not config.model:
            logger.error(f"模型名称未设置: {config.provider.value}")
            return False
            
        return True


def create_llm_from_settings(
    settings: Any, 
    instance_key: Optional[str] = None, 
    langchain_compatible: bool = True,
    provider_override: Optional[Union[str, LLMProvider]] = None
) -> Union[BaseLLM, Any]:
    """
    从配置对象创建LLM实例的便捷函数
    
    Args:
        settings: 配置对象（如config.py中的settings或Worker的config）
        instance_key: 实例缓存键
        langchain_compatible: 是否返回LangChain兼容的适配器
        provider_override: 强制指定提供商（覆盖配置中的设置）
        
    Returns:
        LLM实例或LangChain适配器
    """
    try:
        # 确定使用的提供商
        if provider_override:
            if isinstance(provider_override, str):
                provider_name = provider_override.lower()
            else:
                provider_name = provider_override.value
        else:
            # 从配置中获取提供商
            provider_name = getattr(settings, 'llm_provider', None) or \
                           getattr(settings, 'LLM_PROVIDER', None) or \
                           'deepseek'
            provider_name = provider_name.lower()

        try:
            provider = LLMProvider(provider_name)
        except ValueError:
            logger.warning(f"未知的LLM提供商: {provider_name}, 使用默认的DeepSeek")
            provider = LLMProvider.DEEPSEEK

        # 使用配置构建器创建配置
        config = ConfigBuilder.build_config_from_settings(settings, provider)
        
        # 验证配置
        if not ConfigBuilder.validate_config(config):
            raise LLMError(f"LLM配置无效: {provider.value}")
        
        # 创建LLM实例
        llm = LLMFactory.create_llm(config, instance_key)

        # 如果需要LangChain兼容性，返回适配器
        if langchain_compatible:
            try:
                from .langchain_adapter import create_langchain_llm
                return create_langchain_llm(llm)
            except ImportError:
                logger.warning("LangChain不可用，返回原始LLM实例")
                return llm

        return llm
        
    except Exception as e:
        logger.error(f"从配置创建LLM失败: {e}")
        raise


# 自动注册提供商的装饰器
def register_llm_provider(provider: LLMProvider):
    """
    注册LLM提供商的装饰器
    
    Args:
        provider: 提供商类型
    """
    def decorator(cls: Type[BaseLLM]):
        LLMFactory.register_provider(provider, cls)
        return cls
    return decorator


# 辅助函数
def list_available_providers() -> List[str]:
    """列出所有可用的提供商"""
    return [provider.value for provider in LLMFactory.get_available_providers()]


def get_factory_info() -> Dict[str, Any]:
    """获取工厂信息用于调试"""
    return {
        'available_providers': list_available_providers(),
        'cached_instances': LLMFactory.get_instance_info(),
        'total_cached': len(LLMFactory._instances)
    } 