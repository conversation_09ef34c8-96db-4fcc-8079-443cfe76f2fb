"""
LangChain适配器 - Shared模块
让我们的LLM工厂与LangChain兼容
"""

import logging
from typing import Any, Dict, List, Optional

try:
    from langchain_core.language_models.llms import LLM
    from langchain_core.callbacks.manager import CallbackManagerForLLMRun
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 提供空的基类以避免导入错误
    class LLM:
        pass
    CallbackManagerForLLMRun = Any
    LANGCHAIN_AVAILABLE = False

from .base import BaseLLM

logger = logging.getLogger(__name__)


class LangChainLLMAdapter(LLM):
    """
    LangChain适配器，将我们的BaseLLM适配为LangChain兼容的接口
    """

    def __init__(self, llm: BaseLLM, **kwargs):
        """
        初始化适配器

        Args:
            llm: 我们的BaseLLM实例
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("LangChain不可用，请安装: pip install langchain-core")
            
        super().__init__(**kwargs)
        self._llm = llm
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return f"shared_llm_{self._llm.get_provider().value}"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """
        调用LLM（LangChain LLM基类要求的方法）

        Args:
            prompt: 输入提示
            stop: 停止词列表
            run_manager: 回调管理器
            **kwargs: 额外参数

        Returns:
            响应文本
        """
        try:
            self._logger.debug(f"LangChain适配器调用: {self._llm.get_provider().value}")
            
            # 处理停止词
            if stop:
                kwargs['stop'] = stop
            
            # 调用我们的LLM
            response = self._llm.invoke(prompt, **kwargs)
            
            self._logger.debug(f"LangChain适配器调用成功，响应长度: {len(response.content)}")
            return response.content

        except Exception as e:
            self._logger.error(f"LangChain适配器调用失败: {e}")
            raise

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """返回识别参数"""
        return {
            "provider": self._llm.get_provider().value,
            "model": self._llm.get_model(),
            "temperature": self._llm.config.temperature,
            "max_tokens": self._llm.config.max_tokens,
            "base_url": self._llm.config.base_url,
        }

    def get_num_tokens(self, text: str) -> int:
        """
        获取文本的token数量（近似）
        
        Args:
            text: 输入文本
            
        Returns:
            token数量
        """
        # 简单估算：中文约1字符=1token，英文约4字符=1token
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        return chinese_chars + other_chars // 4

    def get_llm_info(self) -> Dict[str, Any]:
        """获取底层LLM信息"""
        return {
            'provider': self._llm.get_provider().value,
            'model': self._llm.get_model(),
            'config': {
                'temperature': self._llm.config.temperature,
                'max_tokens': self._llm.config.max_tokens,
                'timeout': self._llm.config.timeout,
                'max_retries': self._llm.config.max_retries,
            }
        }

    def __str__(self):
        """字符串表示"""
        return f"LangChainAdapter({self._llm})"

    def __repr__(self):
        """详细字符串表示"""
        return f"LangChainLLMAdapter(llm={repr(self._llm)})"


def create_langchain_llm(llm: BaseLLM, **adapter_kwargs) -> LangChainLLMAdapter:
    """
    创建LangChain兼容的LLM适配器

    Args:
        llm: BaseLLM实例
        **adapter_kwargs: 适配器额外参数

    Returns:
        LangChain兼容的LLM实例

    Raises:
        ImportError: 当LangChain不可用时
    """
    if not LANGCHAIN_AVAILABLE:
        raise ImportError(
            "LangChain不可用，无法创建适配器。"
            "请安装: pip install langchain-core"
        )

    try:
        adapter = LangChainLLMAdapter(llm, **adapter_kwargs)
        logger.info(f"创建LangChain适配器成功: {llm.get_provider().value}")
        return adapter
    except Exception as e:
        logger.error(f"创建LangChain适配器失败: {e}")
        raise


def is_langchain_available() -> bool:
    """检查LangChain是否可用"""
    return LANGCHAIN_AVAILABLE


def get_langchain_version() -> Optional[str]:
    """获取LangChain版本"""
    if not LANGCHAIN_AVAILABLE:
        return None
    
    try:
        import langchain_core
        return getattr(langchain_core, '__version__', 'unknown')
    except:
        return 'unknown' 