"""
分析结果相关数据模型
"""

from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from datetime import datetime


class RiskLevel(str, Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class AnalysisStatus(str, Enum):
    """分析状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AnalysisResult(BaseModel):
    """分析结果模型"""
    analysis_id: str
    file_code: str
    status: AnalysisStatus = AnalysisStatus.PENDING
    default_probability: Optional[float] = None
    risk_level: Optional[RiskLevel] = None
    risk_score: Optional[float] = None
    analysis_summary: Optional[str] = None
    key_factors: Optional[List[str]] = None
    recommendations: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    document_count: Optional[int] = None
    processing_time: Optional[float] = None
    created_at: datetime = datetime.now()
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AnalysisRequest(BaseModel):
    """分析请求模型"""
    file_code: str
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1
    callback_url: Optional[str] = None


class AnalysisResponse(BaseModel):
    """分析响应模型"""
    analysis_id: str
    status: AnalysisStatus
    message: Optional[str] = None
    estimated_time: Optional[int] = None  # 预估完成时间(秒)


class BatchAnalysisRequest(BaseModel):
    """批量分析请求模型"""
    file_codes: List[str]
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1


class BatchAnalysisResponse(BaseModel):
    """批量分析响应模型"""
    batch_id: str
    analysis_ids: List[str]
    total_count: int
    status: str = "submitted"
