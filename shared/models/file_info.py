"""
文件信息相关数据模型
"""

from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime


class FileStatus(str, Enum):
    """文件处理状态"""

    UPLOADING = "uploading"
    PROCESSING = "processing"
    VECTORIZING = "vectorizing"
    COMPLETED = "completed"
    FAILED = "failed"


class FileInfo(BaseModel):
    """文件信息模型"""

    file_code: str
    file_name: str
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    file_type: Optional[str] = None
    content_hash: Optional[str] = None
    status: FileStatus = FileStatus.UPLOADING
    document_count: Optional[int] = None
    created_at: datetime = datetime.now()
    updated_at: datetime = datetime.now()
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class VectorizeRequest(BaseModel):
    """向量化请求模型"""

    file_code: str
    chunk_size: Optional[int] = 4000
    chunk_overlap: Optional[int] = 400
    force_reprocess: bool = False


class VectorizeResponse(BaseModel):
    """向量化响应模型"""

    file_code: str
    status: FileStatus
    document_count: Optional[int] = None
    processing_time: Optional[float] = None
    message: Optional[str] = None


class SearchRequest(BaseModel):
    """向量检索请求模型"""

    file_code: str
    query: str
    top_k: int = 5
    similarity_threshold: Optional[float] = None


class SearchResponse(BaseModel):
    """向量检索响应模型"""

    documents: list
    scores: list
    total_found: int
    query: str
