"""
通用数据模型
"""

from typing import Any, Dict, Optional
from pydantic import BaseModel
from datetime import datetime


class BaseResponse(BaseModel):
    """基础响应模型"""

    success: bool
    message: Optional[str] = None
    timestamp: datetime = datetime.now()

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ErrorResponse(BaseResponse):
    """错误响应模型"""

    success: bool = False
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class SuccessResponse(BaseResponse):
    """成功响应模型"""

    success: bool = True
    data: Optional[Dict[str, Any]] = None
