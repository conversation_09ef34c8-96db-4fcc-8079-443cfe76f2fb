"""
ChromaDB客户端封装
提供统一的ChromaDB访问接口
"""

import chromadb
from chromadb import HttpClient
from typing import List, Dict, Any, Optional, Tuple
from shared.utils.logger import get_logger
from shared.models.file_info import FileInfo

logger = get_logger(__name__)


class ChromaDBClient:
    """ChromaDB客户端封装"""

    def __init__(self, host: str = "localhost", port: int = 8001):
        """
        初始化ChromaDB客户端

        Args:
            host: ChromaDB服务主机
            port: ChromaDB服务端口
        """
        self.host = host
        self.port = port
        self.client = HttpClient(host=host, port=port)
        logger.info(f"ChromaDB client initialized: {host}:{port}")

    def get_collection_name(self, file_code: str) -> str:
        """
        根据文件编码获取集合名称

        Args:
            file_code: 文件编码

        Returns:
            集合名称
        """
        return f"risk_analysis_{file_code}"

    def create_collection(
        self,
        file_code: str,
        embedding_function=None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> chromadb.Collection:
        """
        创建集合

        Args:
            file_code: 文件编码
            embedding_function: 嵌入函数
            metadata: 集合元数据

        Returns:
            集合对象
        """
        collection_name = self.get_collection_name(file_code)

        try:
            collection = self.client.create_collection(
                name=collection_name,
                embedding_function=embedding_function,
                metadata=metadata or {},
            )
            logger.info(f"Created collection: {collection_name}")
            return collection
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {e}")
            raise

    def get_collection(
        self, file_code: str, embedding_function=None
    ) -> chromadb.Collection:
        """
        获取集合

        Args:
            file_code: 文件编码
            embedding_function: 嵌入函数

        Returns:
            集合对象
        """
        collection_name = self.get_collection_name(file_code)

        try:
            collection = self.client.get_collection(
                name=collection_name, embedding_function=embedding_function
            )
            return collection
        except Exception as e:
            logger.error(f"Failed to get collection {collection_name}: {e}")
            raise

    def get_or_create_collection(
        self,
        file_code: str,
        embedding_function=None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> chromadb.Collection:
        """
        获取或创建集合

        Args:
            file_code: 文件编码
            embedding_function: 嵌入函数
            metadata: 集合元数据

        Returns:
            集合对象
        """
        collection_name = self.get_collection_name(file_code)

        try:
            collection = self.client.get_or_create_collection(
                name=collection_name,
                embedding_function=embedding_function,
                metadata=metadata or {},
            )
            return collection
        except Exception as e:
            logger.error(f"Failed to get or create collection {collection_name}: {e}")
            raise

    def delete_collection(self, file_code: str) -> bool:
        """
        删除集合

        Args:
            file_code: 文件编码

        Returns:
            是否删除成功
        """
        collection_name = self.get_collection_name(file_code)

        try:
            self.client.delete_collection(name=collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection {collection_name}: {e}")
            return False

    def list_collections(self) -> List[str]:
        """
        列出所有集合

        Returns:
            集合名称列表
        """
        try:
            collections = self.client.list_collections()
            return [col.name for col in collections]
        except Exception as e:
            logger.error(f"Failed to list collections: {e}")
            return []

    def collection_exists(self, file_code: str) -> bool:
        """
        检查集合是否存在

        Args:
            file_code: 文件编码

        Returns:
            集合是否存在
        """
        collection_name = self.get_collection_name(file_code)
        collections = self.list_collections()
        return collection_name in collections

    def search_documents(
        self,
        file_code: str,
        query_texts: List[str],
        n_results: int = 5,
        where: Optional[Dict[str, Any]] = None,
        embedding_function=None,
    ) -> Dict[str, Any]:
        """
        搜索文档

        Args:
            file_code: 文件编码
            query_texts: 查询文本列表
            n_results: 返回结果数量
            where: 过滤条件
            embedding_function: 嵌入函数

        Returns:
            搜索结果
        """
        try:
            collection = self.get_collection(file_code, embedding_function)
            results = collection.query(
                query_texts=query_texts, n_results=n_results, where=where
            )
            return results
        except Exception as e:
            logger.error(f"Failed to search documents in {file_code}: {e}")
            raise

    def add_documents(
        self,
        file_code: str,
        documents: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        embeddings: Optional[List[List[float]]] = None,
        embedding_function=None,
    ) -> bool:
        """
        添加文档到集合

        Args:
            file_code: 文件编码
            documents: 文档列表
            metadatas: 元数据列表
            ids: 文档ID列表
            embeddings: 嵌入向量列表
            embedding_function: 嵌入函数

        Returns:
            是否添加成功
        """
        try:
            collection = self.get_or_create_collection(file_code, embedding_function)

            # 如果没有提供ID，自动生成
            if ids is None:
                ids = [f"{file_code}_{i}" for i in range(len(documents))]

            collection.add(
                documents=documents, metadatas=metadatas, ids=ids, embeddings=embeddings
            )

            logger.info(f"Added {len(documents)} documents to collection {file_code}")
            return True
        except Exception as e:
            logger.error(f"Failed to add documents to {file_code}: {e}")
            return False

    def get_collection_count(self, file_code: str) -> int:
        """
        获取集合中的文档数量

        Args:
            file_code: 文件编码

        Returns:
            文档数量
        """
        try:
            collection = self.get_collection(file_code)
            return collection.count()
        except Exception as e:
            logger.error(f"Failed to get collection count for {file_code}: {e}")
            return 0

    def health_check(self) -> bool:
        """
        健康检查

        Returns:
            服务是否健康
        """
        try:
            # 尝试列出集合来检查连接
            self.client.list_collections()
            return True
        except Exception as e:
            logger.warning(f"ChromaDB health check failed: {e}")
            return False
