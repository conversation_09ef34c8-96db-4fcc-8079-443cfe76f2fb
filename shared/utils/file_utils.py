"""
文件处理工具
"""

import hashlib
from datetime import datetime
from pathlib import Path
from typing import Union, Optional


def calculate_file_hash(file_path: Union[str, Path], algorithm: str = "md5") -> str:
    """
    计算文件哈希值
    
    Args:
        file_path: 文件路径
        algorithm: 哈希算法 (md5, sha1, sha256)
        
    Returns:
        文件哈希值
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    hash_func = getattr(hashlib, algorithm.lower())()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_func.update(chunk)
    
    return hash_func.hexdigest()


def generate_file_code(
    file_path: Union[str, Path], 
    content_hash: Optional[str] = None
) -> str:
    """
    生成文件编码
    
    Args:
        file_path: 文件路径
        content_hash: 文件内容哈希（可选）
        
    Returns:
        文件编码
    """
    file_path = Path(file_path)
    
    # 基于文件路径和时间戳生成编码
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = file_path.stem
    
    # 如果提供了内容哈希，使用内容哈希
    if content_hash:
        hash_part = content_hash[:8]
    else:
        # 基于文件路径和时间戳生成哈希
        hash_input = f"{file_path}_{timestamp}".encode("utf-8")
        hash_part = hashlib.md5(hash_input).hexdigest()[:8]
    
    file_code = f"{file_name}_{timestamp}_{hash_part}"
    
    return file_code


def get_file_info(file_path: Union[str, Path]) -> dict:
    """
    获取文件基本信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    stat = file_path.stat()
    
    return {
        "name": file_path.name,
        "stem": file_path.stem,
        "suffix": file_path.suffix,
        "size": stat.st_size,
        "created_at": datetime.fromtimestamp(stat.st_ctime),
        "modified_at": datetime.fromtimestamp(stat.st_mtime),
        "is_file": file_path.is_file(),
        "is_dir": file_path.is_dir(),
    }
