"""
配置管理工具
"""

import os
from typing import Any, Optional, Dict
from pathlib import Path
import json
import yaml

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


def get_env_var(key: str, default: Any = None, required: bool = False) -> Any:
    """
    获取环境变量
    
    Args:
        key: 环境变量名
        default: 默认值
        required: 是否必需
        
    Returns:
        环境变量值
        
    Raises:
        ValueError: 当required=True且环境变量不存在时
    """
    value = os.getenv(key, default)
    
    if required and value is None:
        raise ValueError(f"Required environment variable '{key}' is not set")
    
    return value


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        if config_file.suffix.lower() in ['.yml', '.yaml']:
            return yaml.safe_load(f)
        elif config_file.suffix.lower() == '.json':
            return json.load(f)
        else:
            raise ValueError(f"Unsupported config file format: {config_file.suffix}")


class ServiceConfig(BaseSettings):
    """服务基础配置"""
    
    # 服务基本信息
    service_name: str = "unknown"
    service_version: str = "1.0.0"
    debug: bool = False
    
    # 网络配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # 数据库配置
    data_dir: str = "./data"
    cache_dir: str = "./cache"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
