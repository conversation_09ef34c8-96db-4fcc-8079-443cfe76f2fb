"""
Windows兼容的文件操作工具
解决Windows上文件被占用导致删除失败的问题
"""

import os
import sys
import time
import shutil
import logging
from pathlib import Path
from typing import Optional, Union, Callable
import gc

logger = logging.getLogger(__name__)


def is_windows() -> bool:
    """检查是否为Windows系统"""
    return sys.platform.startswith('win') or os.name == 'nt'


def force_garbage_collection():
    """强制垃圾回收以释放文件句柄"""
    gc.collect()
    if is_windows():
        # Windows上可能需要多次gc
        for _ in range(2):  # 优化：减少到2次
            gc.collect()
            time.sleep(0.05)  # 优化：减少延迟


def _handle_deletion_attempt(
    path: Path, 
    attempt: int, 
    max_retries: int, 
    retry_delay: float, 
    force_gc: bool,
    operation: str
) -> tuple[bool, Optional[Exception]]:
    """
    优化：统一的删除重试处理逻辑
    
    Returns:
        (should_continue, last_error)
    """
    try:
        if path.is_file():
            path.unlink()
        elif path.is_dir():
            shutil.rmtree(path)
        
        logger.debug(f"Successfully deleted {operation}: {path}")
        return False, None  # 成功，不需要继续重试
        
    except PermissionError as e:
        if is_windows() and "being used by another process" in str(e):
            logger.warning(
                f"{operation.capitalize()} in use (attempt {attempt + 1}/{max_retries}): {path}"
            )
            
            if attempt < max_retries - 1:
                if force_gc:
                    force_garbage_collection()
                time.sleep(retry_delay * (attempt + 1))  # 增量延迟
                return True, e  # 继续重试
        else:
            logger.error(f"Permission error deleting {operation} {path}: {e}")
        return False, e  # 停止重试
        
    except (OSError, Exception) as e:
        logger.error(f"Error deleting {operation} {path}: {e}")
        return False, e  # 停止重试


def safe_remove_file(
    file_path: Union[str, Path], 
    max_retries: int = 5, 
    retry_delay: float = 0.5,
    force_gc: bool = True
) -> bool:
    """
    安全删除文件，支持重试机制
    
    Args:
        file_path: 文件路径
        max_retries: 最大重试次数
        retry_delay: 重试间隔(秒)
        force_gc: 是否强制垃圾回收
        
    Returns:
        是否删除成功
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        logger.debug(f"File does not exist: {file_path}")
        return True
    
    # 优化：只在Windows上或显式要求时进行垃圾回收
    if force_gc and is_windows():
        force_garbage_collection()
    
    last_error = None
    
    for attempt in range(max_retries):
        should_continue, error = _handle_deletion_attempt(
            file_path, attempt, max_retries, retry_delay, force_gc, "file"
        )
        
        last_error = error
        
        if not should_continue:
            return error is None  # 成功时error为None
    
    logger.error(
        f"Failed to delete file {file_path} after {max_retries} attempts. "
        f"Last error: {last_error}"
    )
    return False


def safe_remove_directory(
    dir_path: Union[str, Path], 
    max_retries: int = 5, 
    retry_delay: float = 0.5,
    force_gc: bool = True
) -> bool:
    """
    安全删除目录，支持重试机制
    
    Args:
        dir_path: 目录路径
        max_retries: 最大重试次数
        retry_delay: 重试间隔(秒)
        force_gc: 是否强制垃圾回收
        
    Returns:
        是否删除成功
    """
    dir_path = Path(dir_path)
    
    if not dir_path.exists():
        logger.debug(f"Directory does not exist: {dir_path}")
        return True
    
    if not dir_path.is_dir():
        logger.warning(f"Path is not a directory: {dir_path}")
        return safe_remove_file(dir_path, max_retries, retry_delay, force_gc)
    
    # 优化：只在Windows上或显式要求时进行垃圾回收
    if force_gc and is_windows():
        force_garbage_collection()
    
    last_error = None
    
    for attempt in range(max_retries):
        should_continue, error = _handle_deletion_attempt(
            dir_path, attempt, max_retries, retry_delay, force_gc, "directory"
        )
        
        last_error = error
        
        if not should_continue:
            return error is None  # 成功时error为None
    
    logger.error(
        f"Failed to delete directory {dir_path} after {max_retries} attempts. "
        f"Last error: {last_error}"
    )
    return False


def with_file_cleanup(cleanup_func: Optional[Callable] = None):
    """
    装饰器：在函数执行后进行文件清理
    
    Args:
        cleanup_func: 可选的清理函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                if cleanup_func:
                    cleanup_func()
                else:
                    force_garbage_collection()
        return wrapper
    return decorator


class WindowsFileCleaner:
    """Windows文件清理器"""
    
    def __init__(self, max_retries: int = 5, retry_delay: float = 0.5):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.pending_deletions = []
    
    def add_for_deletion(self, path: Union[str, Path]):
        """添加路径到待删除列表"""
        self.pending_deletions.append(Path(path))
    
    def cleanup_all(self, force_gc: bool = True) -> dict:
        """
        清理所有待删除的路径
        
        Args:
            force_gc: 是否强制垃圾回收
            
        Returns:
            清理结果统计
        """
        if force_gc:
            force_garbage_collection()
        
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for path in self.pending_deletions:
            if path.is_file():
                success = safe_remove_file(path, self.max_retries, self.retry_delay, False)
            else:
                success = safe_remove_directory(path, self.max_retries, self.retry_delay, False)
            
            if success:
                results["success"] += 1
            else:
                results["failed"] += 1
                results["errors"].append(str(path))
        
        # 清空待删除列表
        self.pending_deletions.clear()
        
        return results 