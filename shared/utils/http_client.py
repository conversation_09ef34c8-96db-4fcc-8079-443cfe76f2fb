"""
HTTP客户端工具
用于服务间通信
"""

import httpx
from typing import Any, Dict, Optional, Union
import json
from shared.utils.logger import get_logger

logger = get_logger(__name__)


class HTTPClient:
    """HTTP客户端封装"""
    
    def __init__(
        self,
        base_url: str,
        timeout: float = 30.0,
        headers: Optional[Dict[str, str]] = None
    ):
        """
        初始化HTTP客户端
        
        Args:
            base_url: 基础URL
            timeout: 超时时间
            headers: 默认请求头
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.default_headers = headers or {}
        
        # 创建httpx客户端
        self.client = httpx.Client(
            base_url=self.base_url,
            timeout=timeout,
            headers=self.default_headers
        )
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()
    
    def close(self):
        """关闭客户端"""
        self.client.close()
    
    async def get(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        GET请求
        
        Args:
            path: 请求路径
            params: 查询参数
            headers: 请求头
            
        Returns:
            响应数据
        """
        try:
            response = self.client.get(
                path,
                params=params,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"GET request failed: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response: {e}")
            raise
    
    async def post(
        self,
        path: str,
        data: Optional[Union[Dict[str, Any], bytes]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        POST请求
        
        Args:
            path: 请求路径
            data: 表单数据
            json_data: JSON数据
            files: 文件数据
            headers: 请求头
            
        Returns:
            响应数据
        """
        try:
            response = self.client.post(
                path,
                data=data,
                json=json_data,
                files=files,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"POST request failed: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response: {e}")
            raise
    
    def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            服务是否健康
        """
        try:
            response = self.client.get("/health", timeout=5.0)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
