"""
统一Redis配置管理器
为Deep Risk RAG项目提供集中的Redis配置和连接管理
"""

import os
import redis
from typing import Optional
from urllib.parse import urlparse

from shared.utils.logger import get_logger

logger = get_logger(__name__)


class RedisConfig:
    """Redis配置管理器"""
    
    def __init__(self):
        """初始化Redis配置"""
        self.host = os.getenv("REDIS_HOST", "*************")
        self.port = int(os.getenv("REDIS_PORT", "7001"))
        self.db = int(os.getenv("REDIS_DB", "5"))  # 项目隔离数据库
        self.password = os.getenv("REDIS_PASSWORD", "redis@2024")
        self.key_prefix = os.getenv("REDIS_KEY_PREFIX", "deep-risk-rag")
        
        # 连接池配置
        self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
        self.socket_timeout = float(os.getenv("REDIS_SOCKET_TIMEOUT", "5.0"))
        self.socket_connect_timeout = float(os.getenv("REDIS_CONNECT_TIMEOUT", "5.0"))
        
        # 连接池
        self._pool = None
        self._client = None
        
        logger.info(f"Redis配置初始化: {self.host}:{self.port}/{self.db} (prefix: {self.key_prefix})")
    
    @property
    def url(self) -> str:
        """获取Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        else:
            return f"redis://{self.host}:{self.port}/{self.db}"
    
    @property
    def pool(self) -> redis.ConnectionPool:
        """获取Redis连接池"""
        if self._pool is None:
            self._pool = redis.ConnectionPool(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                max_connections=self.max_connections,
                socket_timeout=self.socket_timeout,
                socket_connect_timeout=self.socket_connect_timeout,
                decode_responses=True
            )
        return self._pool
    
    @property
    def client(self) -> redis.Redis:
        """获取Redis客户端"""
        if self._client is None:
            self._client = redis.Redis(connection_pool=self.pool)
        return self._client
    
    def get_key(self, key: str) -> str:
        """获取带项目前缀的键名"""
        return f"{self.key_prefix}:{key}"
    
    def get_queue_name(self, queue: str) -> str:
        """获取带项目前缀的队列名"""
        return f"{self.key_prefix}:{queue}"
    
    def test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            self.client.ping()
            logger.info(f"Redis连接测试成功: {self.url}")
            return True
        except Exception as e:
            logger.error(f"Redis连接测试失败: {e}")
            return False
    
    def get_info(self) -> dict:
        """获取Redis服务器信息"""
        try:
            return self.client.info()
        except Exception as e:
            logger.error(f"获取Redis信息失败: {e}")
            return {}
    
    def close(self):
        """关闭Redis连接"""
        if self._client:
            self._client.close()
            self._client = None
        if self._pool:
            self._pool.disconnect()
            self._pool = None
        logger.info("Redis连接已关闭")


# 全局Redis配置实例
redis_config = RedisConfig()


def get_redis_client() -> redis.Redis:
    """获取Redis客户端实例"""
    return redis_config.client


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return redis_config.url


def get_prefixed_key(key: str) -> str:
    """获取带项目前缀的键名"""
    return redis_config.get_key(key)


def get_queue_name(queue: str) -> str:
    """获取带项目前缀的队列名"""
    return redis_config.get_queue_name(queue)


def test_redis_connection() -> bool:
    """测试Redis连接"""
    return redis_config.test_connection() 