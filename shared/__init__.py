"""
Deep Risk RAG 共享组件包
提供微服务架构中的通用组件、数据模型、工具函数和通信协议

这个包设计用于在微服务架构中共享代码，支持：
- 统一的数据模型和响应格式
- LLM工厂和提供商管理
- 系统兼容性自动检测
- Redis和Celery配置管理
- 通用工具函数和HTTP客户端
"""

__version__ = "1.0.0"
__author__ = "Deep Risk RAG Team"
__email__ = "<EMAIL>"

# 核心组件导入
from . import models
from . import llm
from . import utils
from . import protocols
from . import constants

# 配置组件
from .config import load_shared_config, SharedConfig
from .redis_config import get_redis_client, get_redis_url
from .celery_config import create_celery_app
from .system_compatibility import get_system_config
from .service_discovery import service_discovery, initialize_service_discovery

# 常用工具
from .utils import get_logger, setup_logging

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",

    # 核心模块
    "models",
    "llm",
    "utils",
    "protocols",
    "constants",

    # 配置函数
    "load_shared_config",
    "SharedConfig",
    "get_redis_client",
    "get_redis_url",
    "create_celery_app",
    "get_system_config",

    # 服务发现
    "service_discovery",
    "initialize_service_discovery",

    # 常用工具
    "get_logger",
    "setup_logging",
]
