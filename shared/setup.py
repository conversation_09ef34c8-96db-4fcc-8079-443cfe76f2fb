"""
Deep Risk RAG 共享组件包
用于微服务架构中的组件共享
"""

from setuptools import setup, find_packages

# 读取版本信息
def get_version():
    with open("__init__.py", "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                return line.split("=")[1].strip().strip('"').strip("'")
    return "1.0.0"

# 读取依赖
def get_requirements():
    requirements = [
        # 核心依赖
        "pydantic>=2.5.0",
        "redis>=5.0.0",
        "celery>=5.3.0",
        "httpx>=0.25.0",
        
        # 日志和配置
        "python-dotenv>=1.0.0",
        
        # 机器学习相关
        "torch>=2.0.0",
        "numpy>=1.24.0",
        
        # 系统工具
        "psutil>=5.9.0",
        "pandas>=2.0.0",
    ]
    
    return requirements

setup(
    name="deep-risk-rag-shared",
    version=get_version(),
    description="Deep Risk RAG 微服务共享组件",
    long_description=open("README.md", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    
    author="Deep Risk RAG Team",
    author_email="<EMAIL>",
    
    packages=find_packages(),
    python_requires=">=3.10",
    install_requires=get_requirements(),
    
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    
    keywords="microservices, rag, llm, vector-database, shared-components",
    
    # 包数据
    include_package_data=True,
    package_data={
        "shared": ["*.md", "*.txt", "*.yml", "*.yaml"],
    },
    
    # 入口点（如果需要命令行工具）
    entry_points={
        "console_scripts": [
            "shared-config-check=shared.utils.config:check_config",
        ],
    },
    
    # 额外依赖组
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "monitoring": [
            "prometheus-client>=0.17.0",
            "grafana-api>=1.0.0",
        ],
    },
)
