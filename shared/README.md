# Deep Risk RAG 共享组件

这是Deep Risk RAG微服务架构中的共享组件包，提供各服务间通用的数据模型、工具函数和通信协议。

## 架构设计

```
shared/
├── models/          # 数据模型
│   ├── common.py    # 通用响应模型
│   ├── file_info.py # 文件信息模型
│   └── analysis_result.py # 分析结果模型
├── llm/             # LLM相关组件
│   ├── base.py      # 基础LLM接口
│   ├── factory.py   # LLM工厂
│   └── providers/   # LLM提供商实现
├── utils/           # 工具函数
│   ├── logger.py    # 日志工具
│   ├── config.py    # 配置工具
│   ├── file_utils.py # 文件工具
│   └── http_client.py # HTTP客户端
├── protocols/       # 通信协议
│   └── chroma_client.py # ChromaDB客户端协议
├── celery_config.py # Celery配置
├── redis_config.py  # Redis配置
└── system_compatibility.py # 系统兼容性
```

## 使用方式

### 在微服务中使用

由于采用本地路径引用方式，各服务可以直接导入shared模块：

```python
# 在服务代码中
from shared.models import FileInfo, AnalysisResult
from shared.utils import get_logger
from shared.llm import LLMFactory
```

### 环境变量配置

共享组件支持通过环境变量进行配置：

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=INFO

# LLM配置
DEEPSEEK_API_KEY=your_key_here
```

## 核心组件

### 1. 数据模型 (models/)

提供服务间通信的标准数据结构：

- `FileInfo`: 文件信息和状态
- `AnalysisResult`: 风险分析结果
- `BaseResponse`: 统一响应格式

### 2. LLM组件 (llm/)

统一的LLM接口和工厂模式：

- 支持多种LLM提供商（DeepSeek, OpenAI等）
- 统一的配置和错误处理
- 可扩展的提供商架构

### 3. 工具函数 (utils/)

常用的工具函数：

- 日志管理
- 配置加载
- 文件处理
- HTTP客户端

### 4. 系统兼容性 (system_compatibility.py)

自动检测和配置系统兼容性：

- 跨平台支持（Windows, macOS, Linux）
- 自动GPU/CPU检测
- Celery Worker优化配置

## 配置管理

### Redis配置

```python
from shared.redis_config import get_redis_client, get_redis_url

# 获取Redis客户端
client = get_redis_client()

# 获取连接URL
url = get_redis_url()
```

### Celery配置

```python
from shared.celery_config import create_celery_app

# 创建Celery应用
app = create_celery_app("my-service")
```

### 日志配置

```python
from shared.utils import get_logger, setup_logging

# 设置日志
setup_logging(level="INFO", service_name="my-service")

# 获取日志器
logger = get_logger(__name__)
```

## 开发指南

### 添加新的数据模型

1. 在 `models/` 目录下创建新的模型文件
2. 继承 `BaseModel` 或相关基类
3. 在 `models/__init__.py` 中导出

### 添加新的工具函数

1. 在 `utils/` 目录下创建或修改文件
2. 在 `utils/__init__.py` 中导出
3. 添加适当的文档和测试

### 扩展LLM支持

1. 在 `llm/providers/` 下创建新的提供商实现
2. 继承 `BaseLLM` 基类
3. 在工厂中注册新的提供商

## 版本管理

共享组件使用语义化版本控制：

- 主版本号：不兼容的API变更
- 次版本号：向后兼容的功能新增
- 修订版本号：向后兼容的问题修正

## 测试

```bash
# 运行测试
pytest tests/

# 运行特定测试
pytest tests/test_models.py

# 生成覆盖率报告
pytest --cov=shared tests/
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 创建Pull Request

## 许可证

MIT License
