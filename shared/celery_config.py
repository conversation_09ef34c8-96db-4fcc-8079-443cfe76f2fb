"""
Celery配置文件
用于异步任务队列的配置和管理
支持系统兼容性自动检测
"""

import os
from celery import Celery
from kombu import Queue

# 使用统一的Redis配置管理器
from shared.redis_config import redis_config, get_redis_url, get_queue_name

# 导入系统兼容性检测
from shared.system_compatibility import get_system_config, get_celery_worker_args

# Redis配置 (从统一配置管理器获取)
REDIS_URL = get_redis_url()
REDIS_KEY_PREFIX = redis_config.key_prefix

# 获取系统兼容性配置
system_config = get_system_config()

# Celery应用配置
CELERY_CONFIG = {
    # Broker配置 (消息队列)
    'broker_url': REDIS_URL,

    # Result Backend配置 (结果存储)
    'result_backend': REDIS_URL,

    # 任务序列化
    'task_serializer': 'json',
    'result_serializer': 'json',
    'accept_content': ['json'],

    # 时区配置
    'timezone': 'Asia/Shanghai',
    'enable_utc': True,

    # 任务结果过期时间 (24小时)
    'result_expires': 86400,
    
    # 任务路由配置 (使用项目前缀)
    'task_routes': {
        'worker.tasks.vectorize_file': {'queue': get_queue_name('vectorization')},
        'worker.tasks.process_documents': {'queue': get_queue_name('vectorization')},
        'worker.tasks.analyze_risk': {'queue': get_queue_name('analysis')},
        'worker.tasks.health_check': {'queue': get_queue_name('health')},
    },
    
    # 队列配置 (使用项目前缀)
    'task_default_queue': get_queue_name('default'),
    'task_queues': (
        Queue(get_queue_name('default'), routing_key=get_queue_name('default')),
        Queue(get_queue_name('vectorization'), routing_key=get_queue_name('vectorization')),
        Queue(get_queue_name('analysis'), routing_key=get_queue_name('analysis')),
        Queue(get_queue_name('health'), routing_key=get_queue_name('health')),
    ),
    
    # Worker配置 (根据系统兼容性动态调整)
    'worker_prefetch_multiplier': 1,  # 每次只处理一个任务，避免内存问题
    'worker_disable_rate_limits': True,
    
    # 任务执行配置
    'task_acks_late': True,  # 任务完成后才确认
    'task_reject_on_worker_lost': True,  # Worker丢失时拒绝任务
    
    # 监控配置
    'worker_send_task_events': True,
    'task_send_sent_event': True,
    
    # 错误处理
    'task_annotations': {
        '*': {
            'rate_limit': '10/m',  # 每分钟最多10个任务
            'time_limit': 1800,    # 30分钟超时
            'soft_time_limit': 1500,  # 25分钟软超时
        },
        'worker.tasks.vectorize_file': {
            'rate_limit': '5/m',   # 向量化任务更严格的限制
            'time_limit': 3600,    # 1小时超时
            'soft_time_limit': 3300,  # 55分钟软超时
            'retry_kwargs': {'max_retries': 3, 'countdown': 60},
        }
    },
}

# 根据系统兼容性动态调整配置
if system_config.max_tasks_per_child is not None:
    CELERY_CONFIG['worker_max_tasks_per_child'] = system_config.max_tasks_per_child

# 设置多进程启动方式（如果需要）
if system_config.multiprocessing_start_method:
    import multiprocessing
    multiprocessing.set_start_method(system_config.multiprocessing_start_method, force=True)


def create_celery_app(app_name: str = "deep_risk_rag") -> Celery:
    """
    创建Celery应用实例
    
    Args:
        app_name: 应用名称
        
    Returns:
        Celery应用实例
    """
    celery_app = Celery(app_name)
    celery_app.config_from_object(CELERY_CONFIG)
    
    return celery_app


# 创建全局Celery实例
celery_app = create_celery_app()


def get_worker_startup_args() -> dict:
    """
    获取 Worker 启动参数
    根据系统兼容性配置返回最优的启动参数

    Returns:
        Worker 启动参数字典
    """
    args = get_celery_worker_args()

    # 添加其他固定参数
    args.update({
        "loglevel": "info",
        "prefetch_multiplier": 1,
    })

    return args


# 任务状态常量
class TaskStatus:
    """任务状态常量"""
    PENDING = 'PENDING'      # 等待中
    STARTED = 'STARTED'      # 已开始
    SUCCESS = 'SUCCESS'      # 成功
    FAILURE = 'FAILURE'      # 失败
    RETRY = 'RETRY'          # 重试中
    REVOKED = 'REVOKED'      # 已撤销


# 队列名称常量
class QueueNames:
    """队列名称常量"""
    DEFAULT = 'default'
    VECTORIZATION = 'vectorization'
    ANALYSIS = 'analysis'
    HEALTH = 'health'


# 任务名称常量
class TaskNames:
    """任务名称常量"""
    VECTORIZE_FILE = 'worker.tasks.vectorize_file'
    PROCESS_DOCUMENTS = 'worker.tasks.process_documents'
    ANALYZE_RISK = 'worker.tasks.analyze_risk'
    HEALTH_CHECK = 'worker.tasks.health_check'
