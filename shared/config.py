"""
共享配置管理
为微服务提供统一的配置管理接口
"""

import os
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field

from .utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ServiceConfig:
    """服务配置基类"""
    
    # 基本信息
    service_name: str
    service_version: str = "1.0.0"
    
    # 网络配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 运行配置
    debug: bool = False
    log_level: str = "INFO"
    
    # 环境配置
    environment: str = "development"  # development, staging, production
    
    # 额外配置
    extra_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    
    # Redis配置
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # ChromaDB配置
    chroma_host: str = "localhost"
    chroma_port: int = 8000
    chroma_api_version: str = "v1"


@dataclass
class LLMConfig:
    """LLM配置"""
    
    # 默认提供商
    default_provider: str = "deepseek"
    
    # API密钥
    deepseek_api_key: Optional[str] = None
    openai_api_key: Optional[str] = None
    
    # 模型配置
    default_model: str = "deepseek-chat"
    max_tokens: int = 4000
    temperature: float = 0.1
    
    # 流式输出
    enable_streaming: bool = True


@dataclass
class SharedConfig:
    """共享配置容器"""
    
    service: ServiceConfig
    database: DatabaseConfig
    llm: LLMConfig
    
    @classmethod
    def from_env(cls, service_name: str) -> "SharedConfig":
        """从环境变量创建配置"""
        
        # 服务配置
        service = ServiceConfig(
            service_name=service_name,
            service_version=os.getenv("SERVICE_VERSION", "1.0.0"),
            host=os.getenv("HOST", "0.0.0.0"),
            port=int(os.getenv("PORT", "8000")),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            environment=os.getenv("ENVIRONMENT", "development"),
        )
        
        # 数据库配置
        database = DatabaseConfig(
            redis_host=os.getenv("REDIS_HOST", "localhost"),
            redis_port=int(os.getenv("REDIS_PORT", "6379")),
            redis_db=int(os.getenv("REDIS_DB", "0")),
            redis_password=os.getenv("REDIS_PASSWORD"),
            chroma_host=os.getenv("CHROMA_HOST", "localhost"),
            chroma_port=int(os.getenv("CHROMA_PORT", "8000")),
            chroma_api_version=os.getenv("CHROMA_API_VERSION", "v1"),
        )
        
        # LLM配置
        llm = LLMConfig(
            default_provider=os.getenv("LLM_PROVIDER", "deepseek"),
            deepseek_api_key=os.getenv("DEEPSEEK_API_KEY"),
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            default_model=os.getenv("LLM_MODEL", "deepseek-chat"),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "4000")),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.1")),
            enable_streaming=os.getenv("LLM_STREAMING", "true").lower() == "true",
        )
        
        return cls(service=service, database=database, llm=llm)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "service": self.service.__dict__,
            "database": self.database.__dict__,
            "llm": self.llm.__dict__,
        }
    
    def validate(self) -> bool:
        """验证配置"""
        errors = []
        
        # 验证必需的API密钥
        if self.llm.default_provider == "deepseek" and not self.llm.deepseek_api_key:
            errors.append("DEEPSEEK_API_KEY is required when using deepseek provider")
        
        if self.llm.default_provider == "openai" and not self.llm.openai_api_key:
            errors.append("OPENAI_API_KEY is required when using openai provider")
        
        # 验证端口范围
        if not (1 <= self.service.port <= 65535):
            errors.append(f"Invalid port: {self.service.port}")
        
        # 验证日志级别
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.service.log_level.upper() not in valid_log_levels:
            errors.append(f"Invalid log level: {self.service.log_level}")
        
        if errors:
            for error in errors:
                logger.error(f"Configuration error: {error}")
            return False
        
        return True


def load_shared_config(service_name: str) -> SharedConfig:
    """加载共享配置"""
    config = SharedConfig.from_env(service_name)
    
    if not config.validate():
        raise ValueError("Configuration validation failed")
    
    logger.info(f"Loaded configuration for service: {service_name}")
    return config


def get_env_var(key: str, default: Any = None, required: bool = False) -> Any:
    """获取环境变量"""
    value = os.getenv(key, default)
    
    if required and value is None:
        raise ValueError(f"Required environment variable {key} is not set")
    
    return value


def load_config_file(file_path: Union[str, Path]) -> Dict[str, Any]:
    """加载配置文件"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {file_path}")
    
    if file_path.suffix.lower() == ".json":
        import json
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    
    elif file_path.suffix.lower() in [".yml", ".yaml"]:
        try:
            import yaml
            with open(file_path, "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
        except ImportError:
            raise ImportError("PyYAML is required to load YAML configuration files")
    
    else:
        raise ValueError(f"Unsupported configuration file format: {file_path.suffix}")


def check_config():
    """配置检查命令行工具"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: shared-config-check <service_name>")
        sys.exit(1)
    
    service_name = sys.argv[1]
    
    try:
        config = load_shared_config(service_name)
        print(f"✅ Configuration for {service_name} is valid")
        print(f"📊 Configuration summary:")
        print(f"  Service: {config.service.service_name} v{config.service.service_version}")
        print(f"  Host: {config.service.host}:{config.service.port}")
        print(f"  Environment: {config.service.environment}")
        print(f"  Log Level: {config.service.log_level}")
        print(f"  LLM Provider: {config.llm.default_provider}")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        sys.exit(1)
