"""
服务发现和健康检查
为微服务架构提供服务发现和健康检查功能
"""

import asyncio
import httpx
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from .utils.logger import get_logger
from .constants import ServiceNames, ServicePorts, APIRoutes

logger = get_logger(__name__)


@dataclass
class ServiceInfo:
    """服务信息"""
    name: str
    host: str
    port: int
    health_endpoint: str
    is_healthy: bool = False
    last_check: Optional[datetime] = None
    response_time: Optional[float] = None
    error_message: Optional[str] = None


class ServiceDiscovery:
    """服务发现管理器"""
    
    def __init__(self, timeout: float = 5.0):
        self.timeout = timeout
        self.services: Dict[str, ServiceInfo] = {}
        self.http_client = httpx.AsyncClient(timeout=timeout)
        
    async def register_service(
        self, 
        name: str, 
        host: str, 
        port: int, 
        health_endpoint: str = "/health"
    ):
        """注册服务"""
        service = ServiceInfo(
            name=name,
            host=host,
            port=port,
            health_endpoint=health_endpoint
        )
        self.services[name] = service
        logger.info(f"Registered service: {name} at {host}:{port}")
    
    async def register_default_services(self):
        """注册默认服务"""
        services = [
            (ServiceNames.DEEP_SERVICE, "localhost", ServicePorts.DEEP_SERVICE, "/health"),
            (ServiceNames.EMBEDDING_SERVICE, "localhost", ServicePorts.EMBEDDING_SERVICE, "/api/v1/health/ready"),
            ("chromadb", "localhost", ServicePorts.CHROMADB, "/api/v1/heartbeat"),
            ("redis", "localhost", ServicePorts.REDIS, None),  # Redis没有HTTP健康检查
        ]
        
        for name, host, port, health_endpoint in services:
            if health_endpoint:  # 只注册有HTTP健康检查的服务
                await self.register_service(name, host, port, health_endpoint)
    
    async def check_service_health(self, service_name: str) -> bool:
        """检查单个服务健康状态"""
        if service_name not in self.services:
            logger.warning(f"Service {service_name} not registered")
            return False
        
        service = self.services[service_name]
        url = f"http://{service.host}:{service.port}{service.health_endpoint}"
        
        try:
            start_time = datetime.now()
            response = await self.http_client.get(url)
            response_time = (datetime.now() - start_time).total_seconds()
            
            is_healthy = response.status_code == 200
            
            # 更新服务状态
            service.is_healthy = is_healthy
            service.last_check = datetime.now()
            service.response_time = response_time
            service.error_message = None if is_healthy else f"HTTP {response.status_code}"
            
            if is_healthy:
                logger.debug(f"Service {service_name} is healthy (response time: {response_time:.3f}s)")
            else:
                logger.warning(f"Service {service_name} is unhealthy: HTTP {response.status_code}")
            
            return is_healthy
            
        except Exception as e:
            service.is_healthy = False
            service.last_check = datetime.now()
            service.response_time = None
            service.error_message = str(e)
            
            logger.error(f"Health check failed for {service_name}: {e}")
            return False
    
    async def check_all_services(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        results = {}
        
        tasks = [
            self.check_service_health(name) 
            for name in self.services.keys()
        ]
        
        if tasks:
            health_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for service_name, result in zip(self.services.keys(), health_results):
                if isinstance(result, Exception):
                    logger.error(f"Health check error for {service_name}: {result}")
                    results[service_name] = False
                else:
                    results[service_name] = result
        
        return results
    
    def get_service_info(self, service_name: str) -> Optional[ServiceInfo]:
        """获取服务信息"""
        return self.services.get(service_name)
    
    def get_healthy_services(self) -> List[ServiceInfo]:
        """获取健康的服务列表"""
        return [
            service for service in self.services.values() 
            if service.is_healthy
        ]
    
    def get_unhealthy_services(self) -> List[ServiceInfo]:
        """获取不健康的服务列表"""
        return [
            service for service in self.services.values() 
            if not service.is_healthy
        ]
    
    def get_service_url(self, service_name: str, path: str = "") -> Optional[str]:
        """获取服务URL"""
        service = self.services.get(service_name)
        if not service:
            return None
        
        base_url = f"http://{service.host}:{service.port}"
        return f"{base_url}{path}" if path else base_url
    
    async def wait_for_service(
        self, 
        service_name: str, 
        timeout: float = 60.0,
        check_interval: float = 2.0
    ) -> bool:
        """等待服务变为健康状态"""
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < timeout:
            if await self.check_service_health(service_name):
                logger.info(f"Service {service_name} is now healthy")
                return True
            
            logger.info(f"Waiting for service {service_name} to become healthy...")
            await asyncio.sleep(check_interval)
        
        logger.error(f"Timeout waiting for service {service_name} to become healthy")
        return False
    
    async def wait_for_all_services(
        self, 
        timeout: float = 120.0,
        check_interval: float = 5.0
    ) -> bool:
        """等待所有服务变为健康状态"""
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < timeout:
            results = await self.check_all_services()
            
            if all(results.values()):
                logger.info("All services are healthy")
                return True
            
            unhealthy = [name for name, healthy in results.items() if not healthy]
            logger.info(f"Waiting for services to become healthy: {unhealthy}")
            await asyncio.sleep(check_interval)
        
        logger.error("Timeout waiting for all services to become healthy")
        return False
    
    def get_status_summary(self) -> Dict[str, any]:
        """获取状态摘要"""
        healthy_count = len(self.get_healthy_services())
        total_count = len(self.services)
        
        return {
            "total_services": total_count,
            "healthy_services": healthy_count,
            "unhealthy_services": total_count - healthy_count,
            "health_percentage": (healthy_count / total_count * 100) if total_count > 0 else 0,
            "services": {
                name: {
                    "healthy": service.is_healthy,
                    "last_check": service.last_check.isoformat() if service.last_check else None,
                    "response_time": service.response_time,
                    "error": service.error_message,
                }
                for name, service in self.services.items()
            }
        }
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()


# 全局服务发现实例
service_discovery = ServiceDiscovery()


async def initialize_service_discovery():
    """初始化服务发现"""
    await service_discovery.register_default_services()
    logger.info("Service discovery initialized")


async def check_dependencies(required_services: List[str]) -> Tuple[bool, List[str]]:
    """检查服务依赖"""
    results = await service_discovery.check_all_services()
    
    missing_services = []
    for service_name in required_services:
        if service_name not in results or not results[service_name]:
            missing_services.append(service_name)
    
    all_available = len(missing_services) == 0
    return all_available, missing_services


def get_service_url(service_name: str, path: str = "") -> Optional[str]:
    """获取服务URL（同步版本）"""
    return service_discovery.get_service_url(service_name, path)
