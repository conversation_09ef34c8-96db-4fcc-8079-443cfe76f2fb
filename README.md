# Deep Risk RAG - 智能风险分析与问答系统

本项目是一个集成了检索增强生成（RAG）与专业风险预测功能的智能系统。它不仅可以利用本地知识库进行智能问答，还提供了一套基于AI的个人信贷风险预测工具。

## 核心功能

### 通用 RAG 系统
- 🤖 **智能问答**: 支持对接多种大语言模型（如 DeepSeek, OpenAI），提供高质量的对话体验。
- 📚 **本地知识库**: 支持多种文档格式（PDF, DOCX, TXT, MD等），将您的文档转化为可供检索的知识库。
- 🔍 **精准语义检索**: 采用 `bge-m3` 等先进的嵌入模型进行精确的语义搜索。
- 💾 **本地化向量存储**: 基于 ChromaDB，将文档向量存储在本地，确保数据隐私和安全。
- ⚙️ **灵活的LLM切换**: 支持通过配置或代码动态切换不同的语言模型提供商。

### 个人信贷风险预测系统
- 🏦 **AI驱动的风险分析**: 无需预设字段映射，AI能自动分析Excel中的风控特征数据，预测违约概率。
- 📄 **文件级数据隔离**: 每个Excel文件都会创建一个独立的向量数据库，确保不同客户的数据完全隔离。
- 🔐 **唯一文件编码**: 通过为每个文件生成唯一的编码（`file_code`）来管理和查询对应的风险分析结果。
- 📊 **灵活的数据格式**: AI能够自动适应和理解不同结构的Excel风控数据表。
- 📈 **批量处理与API**: 支持通过命令行、交互模式或Python API进行批量风险预测。

## 系统架构

### 🏗️ 微服务架构 (推荐)
- **向量化服务** (`services/vector_service/`) - GPU部署，负责BGE-M3嵌入和ChromaDB管理
- **分析服务** (`services/analysis_service/`) - CPU部署，负责风险分析和LLM调用
- **共享组件** (`shared/`) - 服务间通用的数据模型和工具
- **容器化部署** (`docker/`) - Docker Compose编排，支持分布式部署

### 🔧 技术栈
- **核心框架**: LangChain + FastAPI
- **大语言模型**: DeepSeek, OpenAI 等多种模型支持
- **嵌入模型**: BGE-M3 (GPU加速)
- **向量数据库**: ChromaDB (服务化部署)
- **容器化**: Docker + Docker Compose
- **服务通信**: HTTP REST API

### 📁 服务架构
```
┌─────────────────────┐    ┌─────────────────────┐
│   Vector Service    │    │   Analysis Service  │
│   (GPU Machine)     │    │   (CPU Machine)     │
│                     │    │                     │
│ • BGE-M3 嵌入       │    │ • DeepSeek LLM     │
│ • ChromaDB 服务化   │◄──►│ • 风险分析逻辑      │
│ • 文件上传API       │    │ • 交互式界面        │
│ • 向量化处理        │    │ • HTTP API接口      │
└─────────────────────┘    └─────────────────────┘
```

## 快速开始

### 🚀 微服务部署 (推荐)

#### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd deep-risk-rag

# 运行设置脚本
./scripts/setup.sh
```

#### 2. 配置环境
```bash
# 编辑环境变量文件
vim .env

# 设置必要的API密钥
DEEPSEEK_API_KEY="your_deepseek_api_key_here"
```

#### 3. 启动服务
```bash
# 启动所有微服务
./scripts/start-services.sh

# 检查服务状态
docker-compose ps
```

#### 4. 使用API
```python
import requests

# 1. 上传文件到向量服务
with open("user_data.csv", "rb") as f:
    files = {"file": ("user_data.csv", f, "text/csv")}
    response = requests.post("http://localhost:8002/upload", files=files)
    file_code = response.json()["file_code"]

# 2. 调用分析服务进行风险预测
response = requests.post(f"http://localhost:8003/analyze/{file_code}")
analysis_id = response.json()["analysis_id"]

# 3. 获取分析结果
response = requests.get(f"http://localhost:8003/result/{analysis_id}")
result = response.json()
print(f"违约概率: {result['default_probability']:.2%}")
```

#### 5. 访问API文档
- 向量服务: http://localhost:8002/docs
- 分析服务: http://localhost:8003/docs

### 🔄 传统部署 (兼容模式)

如需使用原有的单体架构：

```bash
# 使用legacy代码
cd legacy

# RAG问答系统
python main.py

# 风险预测系统
python risk_prediction_main.py --interactive
```

## 项目结构

### 🏗️ 微服务架构
```
deep-risk-rag/
├── services/                     # 微服务
│   ├── vector_service/           # 向量化服务 (GPU部署)
│   │   ├── main.py              # FastAPI应用入口
│   │   ├── api/                 # API路由
│   │   ├── core/                # 核心业务逻辑
│   │   └── models/              # 数据模型
│   └── analysis_service/         # 分析服务 (CPU部署)
│       ├── main.py              # FastAPI应用入口
│       ├── api/                 # API路由
│       ├── core/                # 核心业务逻辑
│       └── cli/                 # 命令行工具
├── shared/                       # 共享组件
│   ├── models/                  # 共享数据模型
│   ├── utils/                   # 共享工具
│   └── protocols/               # 服务间通信协议
├── docker/                       # Docker配置
│   ├── docker-compose.yml       # 服务编排
│   ├── vector-service.Dockerfile
│   └── analysis-service.Dockerfile
├── scripts/                      # 部署脚本
│   ├── setup.sh                # 环境设置
│   └── start-services.sh       # 启动服务
├── legacy/                       # 原有代码(兼容)
│   ├── main.py                 # 原RAG系统
│   ├── risk_prediction_main.py # 原风险预测
│   └── src/                    # 原核心代码
├── models/                       # 嵌入模型
├── prompts/                      # LLM指令模板
├── cache/                        # 缓存目录
├── tests/                        # 测试用例
└── docs/                         # 文档
    ├── api/                     # API文档
    ├── deployment/              # 部署文档
    └── migration.md             # 迁移指南
```

## 配置说明

主要的全局配置在 `config.py` 文件中，你可以在此调整：
- `CHUNK_SIZE`: 文档分块大小。
- `RETRIEVAL_TOP_K`: 检索时返回的文档数量。
- `SIMILARITY_THRESHOLD`: 检索结果的相似度阈值。
- `RISK_ANALYSIS_PROMPT_FILE`: 风险分析时使用的指令模板路径。

环境变量（`.env`）用于管理敏感信息，如API密钥。

## 开发与测试

### 运行测试

项目使用 `pytest` 进行测试。

```bash
pytest tests/
```

### 代码规范

请使用 `black` 和 `flake8` 来格式化和检查代码。

```bash
black src/ tests/
flake8 src/ tests/
```

## 贡献

欢迎通过提交 Issue 或 Pull Request 来为项目做出贡献。