# Redis配置文件
# 用于Deep Risk RAG系统

# 基本配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 日志配置
loglevel notice
logfile ""

# 安全配置
# requirepass your_password_here

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 数据库数量
databases 16
