# 系统兼容性指南

本文档介绍如何使用系统兼容性自动检测功能，特别是针对 macOS 系统的 MPS+fork 冲突解决方案。

## 概述

系统兼容性模块会自动检测操作系统并选择最优的 Celery Worker 配置，确保在不同平台上的稳定运行。

## 支持的系统

| 系统 | 默认配置 | 特殊处理 |
|------|----------|----------|
| **macOS** | 线程池 | 提供两种解决方案 |
| **Linux** | 进程池 | 标准配置 |
| **Windows** | 进程池 | 使用 spawn 启动 |

## macOS 解决方案

### 问题背景

在 macOS 系统上，PyTorch 的 MPS (Metal Performance Shaders) 后端与 fork() 操作存在冲突，导致 Worker 进程崩溃：

```
objc[pid]: +[MPSGraphObject initialize] may have been in progress in another thread when fork() was called.
Process 'ForkPoolWorker' exited with 'signal 6 (SIGABRT)'
```

### 解决方案对比

#### 方案 1: 线程池方案（默认推荐）

**优点:**
- ✅ 彻底避免 fork 冲突
- ✅ 架构清晰，安全可靠
- ✅ 自动处理，无需额外配置

**缺点:**
- ⚠️ 改变了 Worker 池类型
- ⚠️ 线程间共享内存

**配置:**
```bash
export MACOS_FORK_SOLUTION=threads  # 默认值
python services/worker_service/worker.py
```

#### 方案 2: 环境变量方案

**优点:**
- ✅ 保持进程池架构
- ✅ 配置简单
- ✅ 享受进程隔离优势

**缺点:**
- ⚠️ 绕过系统安全检查
- ⚠️ 可能存在潜在风险

**配置:**
```bash
export MACOS_FORK_SOLUTION=env_var
python services/worker_service/worker.py
```

## 使用方法

### 自动模式（推荐）

系统会自动检测操作系统并应用最优配置：

```bash
# 直接启动，系统自动选择最优配置
python services/worker_service/worker.py
```

### 手动切换 macOS 方案

```bash
# 使用线程池方案（默认）
export MACOS_FORK_SOLUTION=threads
python services/worker_service/worker.py

# 使用环境变量方案
export MACOS_FORK_SOLUTION=env_var
python services/worker_service/worker.py
```

### 查看当前配置

```bash
python -c "
from shared.system_compatibility import get_system_config
config = get_system_config()
print(f'系统: {config.system_name}')
print(f'池类型: {config.pool_type}')
print(f'设备: {config.device}')
print(f'内存策略: {config.memory_strategy}')
"
```

## 测试工具

### 基础兼容性测试

```bash
python scripts/test_compatibility.py
```

### macOS 双方案对比测试

```bash
python scripts/test_macos_solutions.py
```

### fork 安全性测试

```bash
python scripts/test_fork_safety.py
```

## 配置参数

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MACOS_FORK_SOLUTION` | `threads` | macOS 解决方案类型 |
| `OBJC_DISABLE_INITIALIZE_FORK_SAFETY` | - | 禁用 fork 安全检查（自动设置） |
| `PYTORCH_ENABLE_MPS_FALLBACK` | - | MPS 回退（自动设置） |

### 系统配置

系统会根据检测结果自动设置以下配置：

- **池类型**: `threads` 或 `processes`
- **并发数**: 通常为 `1`
- **设备选择**: `cpu` 或 `auto`
- **内存策略**: `code_cleanup` 或 `process_restart`

## 故障排除

### 常见问题

#### 1. macOS 上仍然出现 fork 冲突

**解决方法:**
```bash
# 确保使用正确的解决方案
export MACOS_FORK_SOLUTION=env_var
python scripts/test_macos_solutions.py
```

#### 2. 线程池模式下内存占用过高

**解决方法:**
- 线程池共享模型，内存占用应该更低
- 检查是否有内存泄漏
- 考虑使用环境变量方案

#### 3. 环境变量方案不生效

**解决方法:**
```bash
# 手动设置环境变量
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export MACOS_FORK_SOLUTION=env_var

# 验证设置
echo $OBJC_DISABLE_INITIALIZE_FORK_SAFETY
echo $MACOS_FORK_SOLUTION
```

### 调试信息

启动时会显示详细的系统兼容性信息：

```
============================================================
🔧 系统兼容性配置
============================================================
系统: macOS (线程池)
Worker池类型: threads
并发数: 1
任务重启阈值: None
设备: cpu
强制CPU: True
内存策略: code_cleanup
启动方式: None
环境变量:
  PYTORCH_ENABLE_MPS_FALLBACK=1
  PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0
============================================================
```

## 最佳实践

### 生产环境

- **macOS**: 使用线程池方案，更安全可靠
- **Linux**: 使用进程池，享受完全隔离
- **Windows**: 使用进程池，spawn 启动方式

### 开发环境

- 可以根据需要切换 macOS 方案
- 使用测试工具验证配置
- 关注日志输出，及时发现问题

### 监控建议

- 监控 Worker 进程状态
- 关注内存使用情况
- 定期运行兼容性测试

## 技术细节

### 实现原理

1. **系统检测**: 使用 `platform.system()` 检测操作系统
2. **配置选择**: 根据系统类型和用户偏好选择配置
3. **环境变量**: 自动设置必要的环境变量
4. **动态配置**: Celery 配置根据检测结果动态调整

### 关键文件

- `shared/system_compatibility.py` - 核心兼容性检测模块
- `shared/celery_config.py` - 动态 Celery 配置
- `services/worker_service/config.py` - Worker 服务配置
- `services/worker_service/core/embeddings.py` - 嵌入服务适配

## 更新日志

### v1.0.0
- 实现基础系统兼容性检测
- 支持 macOS 线程池方案

### v1.1.0
- 新增 macOS 环境变量方案
- 提供双方案选择
- 完善测试工具

## 贡献

如果发现新的兼容性问题或有改进建议，请：

1. 运行相关测试脚本
2. 收集详细的错误信息
3. 提交 Issue 或 Pull Request

## 参考资料

- [Apple Developer Documentation - Metal Performance Shaders](https://developer.apple.com/documentation/metalperformanceshaders)
- [PyTorch MPS Backend](https://pytorch.org/docs/stable/notes/mps.html)
- [Celery Documentation - Workers](https://docs.celeryproject.org/en/stable/userguide/workers.html)
