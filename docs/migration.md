# Deep Risk RAG 微服务架构迁移指南

## 概述

本文档描述了从单体架构向微服务架构的迁移过程，以及新架构的使用方法。

## 架构变化

### 原架构
```
单体应用
├── main.py (RAG问答)
├── risk_prediction_main.py (风险预测)
└── src/ (共享代码)
```

### 新架构
```
微服务架构
├── services/vector_service/ (向量化服务 - GPU部署)
├── services/analysis_service/ (分析服务 - CPU部署)
├── shared/ (共享组件)
└── docker/ (容器化配置)
```

## 服务职责

### 向量化服务 (Vector Service)
- **部署位置**: GPU机器
- **端口**: 8002
- **职责**:
  - 文件上传和处理
  - BGE-M3嵌入生成
  - ChromaDB向量存储管理
  - 向量检索服务

### 分析服务 (Analysis Service)
- **部署位置**: CPU机器
- **端口**: 8003
- **职责**:
  - 风险分析逻辑
  - DeepSeek LLM调用
  - 保留原有交互式界面
  - 提供HTTP API接口

## 迁移步骤

### 1. 环境准备
```bash
# 运行设置脚本
./scripts/setup.sh

# 编辑环境变量
vim .env
```

### 2. 启动服务
```bash
# 启动所有服务
./scripts/start-services.sh

# 检查服务状态
docker-compose ps
```

### 3. 数据迁移
```bash
# 迁移现有ChromaDB数据
./scripts/migrate-data.sh
```

## API使用示例

### 向量化服务API

#### 上传文件
```python
import requests

# 上传CSV文件
with open("user_data.csv", "rb") as f:
    files = {"file": ("user_data.csv", f, "text/csv")}
    response = requests.post("http://localhost:8002/upload", files=files)
    
result = response.json()
file_code = result["file_code"]
print(f"文件编码: {file_code}")
```

#### 查询状态
```python
# 查询向量化状态
response = requests.get(f"http://localhost:8002/status/{file_code}")
status = response.json()
print(f"状态: {status['status']}")
```

### 分析服务API

#### 风险分析
```python
# 开始风险分析
response = requests.post(f"http://localhost:8003/analyze/{file_code}")
analysis = response.json()
analysis_id = analysis["analysis_id"]

# 获取分析结果
response = requests.get(f"http://localhost:8003/result/{analysis_id}")
result = response.json()
print(f"违约概率: {result['default_probability']:.2%}")
```

## 兼容性保持

### 命令行工具
原有的交互式命令行工具仍然可用：

```bash
# 进入分析服务容器
docker exec -it deep-risk-analysis-service bash

# 运行交互式工具
python cli/interactive.py
```

### Python API
提供兼容的Python客户端：

```python
from shared.protocols import VectorServiceClient, AnalysisServiceClient

# 使用客户端
async with VectorServiceClient("http://localhost:8002") as vector_client:
    result = await vector_client.upload_file("data.csv", file_data, "data.csv")
    
async with AnalysisServiceClient("http://localhost:8003") as analysis_client:
    analysis = await analysis_client.start_analysis(file_code)
```

## 部署配置

### GPU部署 (向量服务)
```yaml
# docker-compose.yml
vector-service:
  deploy:
    resources:
      reservations:
        devices:
          - driver: nvidia
            count: 1
            capabilities: [gpu]
```

### 环境变量配置
```bash
# .env
DEEPSEEK_API_KEY=your_api_key
VECTOR_SERVICE_URL=http://vector-service:8000
CHROMA_HOST=chromadb
CHROMA_PORT=8000
```

## 监控和日志

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f vector-service
docker-compose logs -f analysis-service
```

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8002/health  # 向量服务
curl http://localhost:8003/health  # 分析服务
```

## 故障排除

### 常见问题

1. **GPU不可用**
   - 检查NVIDIA Docker安装
   - 确认GPU驱动正常

2. **服务无法启动**
   - 检查端口占用
   - 查看Docker日志

3. **API调用失败**
   - 确认服务健康状态
   - 检查网络连接

### 回滚方案
如需回滚到原架构：
```bash
# 停止新服务
docker-compose down

# 使用legacy代码
cd legacy
python main.py  # RAG问答
python risk_prediction_main.py  # 风险预测
```
