# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# AI/ML specific
models/bge-m3-safetensors-only/
models/*.safetensors
models/*.bin
models/*.pt
models/*.onnx
models/.cache/
*.pkl
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
checkpoints/
logs/
tensorboard_logs/
wandb/

# Vector databases
vector_db/
chroma_db/
faiss_index/
*.index
*.faiss

# Data files
data/
datasets/
*.csv
*.xlsx
*.xls
*.json
*.parquet
*.feather

# Configuration files with sensitive info
config.ini
secrets.yaml
.secrets
api_keys.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Cache directories
.cache/
cache/

# Output files
output/
results/
reports/

# Backup files
*.bak
*.backup

# Risk analysis specific
risk_reports/
analysis_output/
prediction_results/
.augment-guidelines
issues/
.cursor