"""
LLM基础接口定义 - Shared模块
统一的LLM抽象类和配置
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """LLM提供商枚举"""
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"


@dataclass
class LLMConfig:
    """LLM配置数据类"""
    provider: LLMProvider
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = ""
    temperature: float = 0.1
    max_tokens: Optional[int] = None
    timeout: int = 60
    max_retries: int = 3
    # 扩展配置，用于特定提供商的额外参数
    extra_params: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """后处理验证"""
        if self.extra_params is None:
            self.extra_params = {}
        
        # 验证必要参数
        if not self.model:
            logger.warning(f"模型名称为空: {self.provider.value}")


@dataclass 
class LLMResponse:
    """LLM响应数据类"""
    content: str
    model: str
    usage: Optional[Dict[str, Any]] = None
    raw_response: Optional[Any] = None
    finish_reason: Optional[str] = None
    
    def __post_init__(self):
        """后处理"""
        if self.usage is None:
            self.usage = {}


# 异常类定义
class LLMError(Exception):
    """LLM基础异常"""
    pass


class LLMConnectionError(LLMError):
    """连接错误"""
    pass


class LLMAuthenticationError(LLMError):
    """认证错误"""
    pass


class LLMRateLimitError(LLMError):
    """速率限制错误"""
    pass


class LLMInvalidRequestError(LLMError):
    """无效请求错误"""
    pass


class BaseLLM(ABC):
    """LLM基础抽象类"""

    def __init__(self, config: LLMConfig):
        """
        初始化LLM实例

        Args:
            config: LLM配置
        """
        self.config = config
        self._client = None
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        try:
            self._initialize_client()
            self._logger.info(f"LLM实例初始化成功: {config.provider.value} - {config.model}")
        except Exception as e:
            self._logger.error(f"LLM实例初始化失败: {config.provider.value} - {e}")
            raise

    @abstractmethod
    def _initialize_client(self):
        """初始化LLM客户端 - 子类必须实现"""
        pass

    @abstractmethod
    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用LLM生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLM响应
        """
        pass

    @abstractmethod
    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用LLM

        Args:
            prompts: 提示列表
            **kwargs: 额外参数

        Returns:
            响应列表
        """
        pass

    @abstractmethod
    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用LLM

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Yields:
            流式响应片段
        """
        pass

    def get_provider(self) -> LLMProvider:
        """获取提供商类型"""
        return self.config.provider

    def get_model(self) -> str:
        """获取模型名称"""
        return self.config.model

    def update_config(self, **kwargs):
        """
        更新配置
        
        Args:
            **kwargs: 要更新的配置项
        """
        config_updated = False
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                config_updated = True
                self._logger.debug(f"更新配置 {key}: {value}")
        
        if config_updated:
            # 重新初始化客户端
            try:
                self._initialize_client()
                self._logger.info("配置更新后重新初始化客户端成功")
            except Exception as e:
                self._logger.error(f"配置更新后重新初始化客户端失败: {e}")
                raise

    def _merge_call_params(self, **kwargs) -> Dict[str, Any]:
        """
        合并调用参数 - 减少重复代码
        
        Args:
            **kwargs: 调用时的额外参数
            
        Returns:
            合并后的参数字典
        """
        return {
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            **{k: v for k, v in kwargs.items() 
               if k not in ["temperature", "max_tokens"]}
        }

    def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            是否健康
        """
        try:
            # 使用更适合的参数进行健康检查
            response = self.invoke("test", max_tokens=10)
            # 检查响应是否存在以及API调用是否成功
            # 对于健康检查，API调用成功比响应内容更重要
            is_healthy = response is not None
            
            if is_healthy:
                self._logger.info(f"LLM健康检查通过: 响应长度={len(response.content) if response.content else 0}")
            else:
                self._logger.warning("LLM健康检查失败: 无响应")
                
            return is_healthy
        except Exception as e:
            self._logger.warning(f"健康检查失败: {e}")
            return False

    def __str__(self):
        """字符串表示"""
        return f"LLM({self.config.provider.value}:{self.config.model})"

    def __repr__(self):
        """详细字符串表示"""
        return (f"BaseLLM(provider={self.config.provider.value}, "
                f"model={self.config.model}, "
                f"temperature={self.config.temperature})") 