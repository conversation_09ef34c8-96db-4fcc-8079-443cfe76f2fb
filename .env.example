# Deep Risk RAG 异步架构环境配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 基本服务配置
# =============================================================================

# 服务运行模式 (dev/prod)
NODE_ENV=dev

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 主机和端口配置
HOST=0.0.0.0
PORT=8000

# =============================================================================
# LLM配置
# =============================================================================

# LLM提供商 (deepseek/openai)
LLM_PROVIDER=deepseek

# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-reasoner

# OpenAI配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# 是否启用流式输出
ENABLE_STREAMING=true

# =============================================================================
# Redis配置 (Celery消息队列)
# =============================================================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# ChromaDB配置
# =============================================================================

CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_PERSIST_DIR=./data/chromadb
CHROMA_COLLECTION_PREFIX=file_
CHROMA_AUTH_ENABLED=false
CHROMA_AUTH_TOKEN=
CHROMA_ALLOW_RESET=true
ANONYMIZED_TELEMETRY=False

# =============================================================================
# BGE-M3模型配置
# =============================================================================

# 模型路径 (本地路径)
MODEL_NAME=./models/bge-m3-safetensors-only

# 设备配置 (auto/cpu/cuda)
DEVICE=auto

# 模型参数
BATCH_SIZE=32
MAX_LENGTH=8192
NORMALIZE_EMBEDDINGS=true

# 内存管理
ENABLE_MEMORY_MONITORING=true
AUTO_CLEANUP=true
MEMORY_CLEANUP_THRESHOLD=80.0

# =============================================================================
# 文件处理配置
# =============================================================================

# 上传目录
UPLOAD_DIR=./data/uploads

# 文件大小限制 (MB)
MAX_FILE_SIZE=100

# 支持的文件类型
ALLOWED_EXTENSIONS=.csv,.xlsx,.xls

# 文档分块配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNKS_PER_FILE=100

# =============================================================================
# 任务配置
# =============================================================================

# 任务超时 (秒)
TASK_TIMEOUT=1800
TASK_SOFT_TIMEOUT=1500

# 重试配置
TASK_RETRY_MAX=3
TASK_RETRY_DELAY=60

# Worker配置
CELERY_WORKER_CONCURRENCY=1
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_WORKER_MAX_TASKS_PER_CHILD=10

# =============================================================================
# 提示词配置
# =============================================================================

# 提示词目录
PROMPTS_DIR=./prompts

# =============================================================================
# 监控配置
# =============================================================================

# Flower监控端口
FLOWER_PORT=5555

# =============================================================================
# 开发配置
# =============================================================================

# 调试模式
DEBUG=false

# 热重载 (开发模式)
RELOAD=true
