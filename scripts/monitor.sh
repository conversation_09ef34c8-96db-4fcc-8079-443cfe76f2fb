#!/bin/bash

# Deep Risk RAG 系统监控脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Deep Risk RAG 系统监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --status     显示服务状态"
    echo "  -l, --logs       显示实时日志"
    echo "  -m, --metrics    显示系统指标"
    echo "  -t, --tasks      显示任务状态"
    echo "  -w, --watch      持续监控模式"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -s            # 显示服务状态"
    echo "  $0 -w            # 持续监控"
    echo "  $0 -l            # 显示实时日志"
}

# 检查服务状态
check_service_status() {
    print_header "📊 服务状态检查"
    echo ""
    
    local services=("redis" "chromadb" "unified" "worker" "flower")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        local status="❌ 未运行"
        local details=""
        
        case $service in
            redis)
                if redis-cli ping &> /dev/null; then
                    status="✅ 运行中"
                    local info=$(redis-cli info server | grep redis_version | cut -d: -f2 | tr -d '\r')
                    details="版本: $info"
                else
                    all_healthy=false
                fi
                ;;
            chromadb)
                if curl -f http://localhost:8001/api/v1/heartbeat &> /dev/null; then
                    status="✅ 运行中"
                    details="端口: 8001"
                else
                    all_healthy=false
                fi
                ;;
            unified)
                if curl -f http://localhost:8000/health &> /dev/null; then
                    status="✅ 运行中"
                    details="端口: 8000"
                else
                    all_healthy=false
                fi
                ;;
            worker)
                local pid_file="pids/worker.pid"
                if [ -f "$pid_file" ]; then
                    local pid=$(cat "$pid_file")
                    if kill -0 "$pid" 2>/dev/null; then
                        status="✅ 运行中"
                        details="PID: $pid"
                    else
                        all_healthy=false
                    fi
                else
                    all_healthy=false
                fi
                ;;
            flower)
                if curl -f http://localhost:5555 &> /dev/null; then
                    status="✅ 运行中"
                    details="端口: 5555"
                else
                    all_healthy=false
                fi
                ;;
        esac
        
        printf "%-15s %s %s\n" "$service:" "$status" "$details"
    done
    
    echo ""
    if [ "$all_healthy" = true ]; then
        print_message "🎉 所有服务运行正常"
    else
        print_warning "⚠️  部分服务异常，请检查"
    fi
}

# 显示系统指标
show_system_metrics() {
    print_header "📈 系统资源使用情况"
    echo ""
    
    # CPU使用率
    local cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    echo "💻 CPU使用率: ${cpu_usage}%"
    
    # 内存使用情况
    local memory_info=$(vm_stat | grep -E "(free|active|inactive|wired)" | awk '{print $3}' | sed 's/\.//')
    local page_size=4096
    local free_pages=$(echo "$memory_info" | sed -n '1p')
    local active_pages=$(echo "$memory_info" | sed -n '2p')
    local inactive_pages=$(echo "$memory_info" | sed -n '3p')
    local wired_pages=$(echo "$memory_info" | sed -n '4p')
    
    local total_memory=$((($free_pages + $active_pages + $inactive_pages + $wired_pages) * $page_size / 1024 / 1024))
    local used_memory=$((($active_pages + $inactive_pages + $wired_pages) * $page_size / 1024 / 1024))
    local memory_percent=$((used_memory * 100 / total_memory))
    
    echo "🧠 内存使用: ${used_memory}MB / ${total_memory}MB (${memory_percent}%)"
    
    # 磁盘使用情况
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
    local disk_available=$(df -h . | tail -1 | awk '{print $4}')
    echo "💾 磁盘使用: ${disk_usage}% (可用: ${disk_available})"
    
    # 网络连接
    local connections=$(netstat -an | grep ESTABLISHED | wc -l | tr -d ' ')
    echo "🌐 网络连接: ${connections} 个活跃连接"
    
    echo ""
    
    # GPU信息（如果可用）
    if command -v nvidia-smi &> /dev/null; then
        print_header "🎮 GPU使用情况"
        nvidia-smi --query-gpu=name,utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits | while read line; do
            echo "  $line"
        done
        echo ""
    fi
}

# 显示任务状态
show_task_status() {
    print_header "⚙️  Celery任务状态"
    echo ""
    
    # 检查Celery是否可用
    if ! command -v celery &> /dev/null; then
        print_error "Celery未安装或不在PATH中"
        return 1
    fi
    
    # 显示活跃任务
    echo "📋 活跃任务:"
    celery -A services.worker_service.worker inspect active 2>/dev/null || echo "  无活跃任务或Worker未运行"
    
    echo ""
    echo "📊 任务统计:"
    celery -A services.worker_service.worker inspect stats 2>/dev/null || echo "  无法获取统计信息"
    
    echo ""
    echo "🔄 队列状态:"
    # 检查Redis队列长度
    if redis-cli ping &> /dev/null; then
        local vectorization_queue=$(redis-cli llen celery 2>/dev/null || echo "0")
        local analysis_queue=$(redis-cli llen analysis 2>/dev/null || echo "0")
        echo "  向量化队列: $vectorization_queue 个任务"
        echo "  分析队列: $analysis_queue 个任务"
    else
        echo "  Redis未运行，无法检查队列状态"
    fi
}

# 显示实时日志
show_logs() {
    print_header "📋 实时日志监控"
    echo ""
    echo "按 Ctrl+C 停止日志监控"
    echo ""
    
    # 检查日志文件是否存在
    local log_files=(logs/*.log)
    if [ ! -f "${log_files[0]}" ]; then
        print_warning "没有找到日志文件"
        return 1
    fi
    
    # 显示实时日志
    tail -f logs/*.log 2>/dev/null
}

# 持续监控模式
watch_mode() {
    print_header "👀 持续监控模式"
    echo "按 Ctrl+C 退出监控"
    echo ""
    
    while true; do
        clear
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Deep Risk RAG 系统监控"
        echo "========================================================"
        
        check_service_status
        echo ""
        show_system_metrics
        echo ""
        show_task_status
        
        echo ""
        echo "下次更新: $(date -d '+30 seconds' '+%H:%M:%S')"
        
        sleep 30
    done
}

# 检查ChromaDB统计
check_chromadb_stats() {
    print_header "🗄️  ChromaDB统计信息"
    echo ""
    
    if curl -f http://localhost:8001/api/v1/heartbeat &> /dev/null; then
        # 使用ChromaDB CLI工具
        if [ -f "scripts/chromadb_cli.py" ]; then
            python scripts/chromadb_cli.py stats
        else
            echo "ChromaDB CLI工具不可用"
        fi
    else
        print_error "ChromaDB服务未运行"
    fi
}

# 生成监控报告
generate_report() {
    local report_file="logs/monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    print_header "📄 生成监控报告"
    echo ""
    
    {
        echo "Deep Risk RAG 系统监控报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "服务状态:"
        check_service_status
        echo ""
        
        echo "系统指标:"
        show_system_metrics
        echo ""
        
        echo "任务状态:"
        show_task_status
        echo ""
        
        echo "ChromaDB统计:"
        check_chromadb_stats
        
    } > "$report_file"
    
    print_message "监控报告已保存到: $report_file"
}

# 默认参数
ACTION="status"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--status)
            ACTION="status"
            shift
            ;;
        -l|--logs)
            ACTION="logs"
            shift
            ;;
        -m|--metrics)
            ACTION="metrics"
            shift
            ;;
        -t|--tasks)
            ACTION="tasks"
            shift
            ;;
        -w|--watch)
            ACTION="watch"
            shift
            ;;
        -r|--report)
            ACTION="report"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
main() {
    case $ACTION in
        status)
            check_service_status
            ;;
        logs)
            show_logs
            ;;
        metrics)
            show_system_metrics
            ;;
        tasks)
            show_task_status
            ;;
        watch)
            watch_mode
            ;;
        report)
            generate_report
            ;;
        *)
            print_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main
