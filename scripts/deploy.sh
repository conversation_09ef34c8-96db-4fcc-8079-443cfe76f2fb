#!/bin/bash

# Deep Risk RAG 微服务部署脚本
# 支持本地Docker部署

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Deep Risk RAG 微服务部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --up       启动所有服务"
    echo "  -d, --down     停止所有服务"
    echo "  -r, --restart  重启所有服务"
    echo "  -s, --status   查看服务状态"
    echo "  -l, --logs     查看服务日志"
    echo "  -c, --clean    清理所有数据（危险操作）"
    echo ""
    echo "示例:"
    echo "  $0 --up       # 启动所有服务"
    echo "  $0 --logs     # 查看实时日志"
    echo "  $0 --status   # 检查服务状态"
    echo ""
}

# 检查环境
check_environment() {
    print_header "🔍 检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查环境文件
    if [ ! -f "deployment/.env" ]; then
        print_warning "环境配置文件不存在，从示例文件创建..."
        cp deployment/.env.example deployment/.env
        print_message "请编辑 deployment/.env 文件配置必要的环境变量"
    fi
    
    print_message "环境检查完成"
}

# 启动服务
start_services() {
    print_header "🚀 启动Deep Risk RAG微服务..."
    
    cd deployment
    
    # 构建镜像
    print_message "构建Docker镜像..."
    docker-compose build
    
    # 启动服务
    print_message "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_message "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services_health
    
    print_message "服务启动完成！"
    show_access_info
}

# 停止服务
stop_services() {
    print_header "🛑 停止Deep Risk RAG微服务..."
    
    cd deployment
    docker-compose down
    
    print_message "服务已停止"
}

# 重启服务
restart_services() {
    print_header "🔄 重启Deep Risk RAG微服务..."
    
    stop_services
    sleep 5
    start_services
}

# 检查服务健康状态
check_services_health() {
    print_header "📊 检查服务健康状态..."
    
    local services=(
        "http://localhost:8000/health:Deep服务"
        "http://localhost:8004/api/v1/health/ready:嵌入服务"
        "http://localhost:8001/api/v1/heartbeat:ChromaDB"
        "http://localhost:5555:Flower监控"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r url name <<< "$service"
        
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo "✅ $name: 运行正常"
        else
            echo "❌ $name: 服务异常"
        fi
    done
}

# 查看服务状态
show_status() {
    print_header "📋 服务状态..."
    
    cd deployment
    docker-compose ps
    
    echo ""
    check_services_health
}

# 查看日志
show_logs() {
    print_header "📋 服务日志..."
    
    cd deployment
    docker-compose logs -f
}

# 清理数据
clean_data() {
    print_header "🧹 清理所有数据..."
    
    read -p "⚠️  这将删除所有数据，确定继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd deployment
        docker-compose down -v
        docker system prune -f
        print_message "数据清理完成"
    else
        print_message "操作已取消"
    fi
}

# 显示访问信息
show_access_info() {
    print_header "🌐 服务访问信息:"
    echo ""
    echo "📱 主要服务:"
    echo "  Deep服务:     http://localhost:8000"
    echo "  API文档:      http://localhost:8000/docs"
    echo "  嵌入服务:     http://localhost:8004"
    echo "  嵌入API文档:  http://localhost:8004/docs"
    echo ""
    echo "🔧 管理工具:"
    echo "  ChromaDB:     http://localhost:8001"
    echo "  Flower监控:   http://localhost:5555"
    echo ""
    echo "📋 常用命令:"
    echo "  查看状态:     $0 --status"
    echo "  查看日志:     $0 --logs"
    echo "  重启服务:     $0 --restart"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -u|--up)
            check_environment
            start_services
            ;;
        -d|--down)
            stop_services
            ;;
        -r|--restart)
            check_environment
            restart_services
            ;;
        -s|--status)
            show_status
            ;;
        -l|--logs)
            show_logs
            ;;
        -c|--clean)
            clean_data
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
