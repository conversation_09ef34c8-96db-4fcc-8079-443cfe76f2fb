#!/usr/bin/env python3
"""
验证遥测数据发送是否已被完全禁用
确保没有数据被发送到外部服务
"""

import os
import sys


def check_telemetry_disabled():
    """检查遥测禁用状态"""
    print("🔒 检查遥测禁用状态...")
    print("=" * 60)

    # 需要检查的环境变量
    telemetry_vars = {
        "PostHog遥测": [
            ("POSTHOG_HOST", ""),
            ("POSTHOG_PROJECT_ID", ""),
            ("POSTHOG_API_KEY", ""),
            ("POSTHOG_FEATURE_FLAGS", "false"),
        ],
        "LangChain遥测": [
            ("LANGCHAIN_TRACING_V2", "false"),
            ("LANGCHAIN_ANALYTICS", "false"),
            ("LANGCHAIN_TRACING", "false"),
            ("LANGCHAIN_TRACKING", "false"),
            ("LANGCHAIN_ENDPOINT", ""),
            ("LANGCHAIN_API_KEY", ""),
        ],
        "通用遥测": [
            ("TELEMETRY_DISABLED", "true"),
            ("DO_NOT_TRACK", "1"),
            ("ANALYTICS_DISABLED", "true"),
        ],
    }

    all_disabled = True

    for category, vars_list in telemetry_vars.items():
        print(f"\n📋 {category}:")
        category_ok = True

        for var_name, expected_value in vars_list:
            actual_value = os.environ.get(var_name, "未设置")

            if actual_value == expected_value:
                status = "✅ 已禁用"
            else:
                status = "❌ 未禁用"
                category_ok = False
                all_disabled = False

            print(f"   {var_name}: {actual_value} {status}")

        if category_ok:
            print(f"   → {category} 完全禁用 ✅")
        else:
            print(f"   → {category} 部分未禁用 ⚠️")

    print("\n" + "=" * 60)

    if all_disabled:
        print("🎉 所有遥测功能已完全禁用!")
        print("✅ 隐私和数据安全得到保护")
        print("✅ 不会有数据发送到外部服务")
        return True
    else:
        print("⚠️  发现未完全禁用的遥测功能")
        print("💡 建议运行启动脚本或重新设置环境变量")
        return False


def check_network_connections():
    """检查是否有到PostHog等服务的网络连接"""
    print("\n🌐 检查网络连接...")

    suspicious_hosts = [
        "us.i.posthog.com",
        "app.posthog.com",
        "api.posthog.com",
        "eu.posthog.com",
    ]

    try:
        import socket

        for host in suspicious_hosts:
            try:
                # 尝试解析域名，但不建立连接
                socket.gethostbyname(host)
                print(f"   {host}: 可访问 (但已禁用连接)")
            except socket.gaierror:
                print(f"   {host}: 无法解析 ✅")

    except ImportError:
        print("   无法检查网络连接（socket模块不可用）")

    print("💡 即使域名可访问，由于环境变量设置，不会建立实际连接")


def simulate_library_import():
    """模拟导入可能发送遥测的库"""
    print("\n📚 测试库导入时的遥测状态...")

    # 保存当前的print函数，用于捕获输出
    original_print = print
    captured_output = []

    def capture_print(*args, **kwargs):
        captured_output.append(" ".join(str(arg) for arg in args))
        original_print(*args, **kwargs)

    try:
        # 临时替换print函数来捕获可能的遥测信息
        import builtins

        builtins.print = capture_print

        # 尝试导入可能发送遥测的库
        test_imports = [
            "langchain",
            "langchain_core",
            "langchain_chroma",
            "sentence_transformers",
        ]

        for lib_name in test_imports:
            try:
                __import__(lib_name)
                print(f"   {lib_name}: 导入成功，无遥测警告 ✅")
            except ImportError:
                print(f"   {lib_name}: 未安装")
            except Exception as e:
                print(f"   {lib_name}: 导入异常 - {e}")

    finally:
        # 恢复原始print函数
        builtins.print = original_print

    # 检查是否有遥测相关的输出
    telemetry_keywords = ["posthog", "analytics", "tracking", "telemetry"]
    suspicious_output = [
        output
        for output in captured_output
        if any(keyword in output.lower() for keyword in telemetry_keywords)
    ]

    if suspicious_output:
        print("⚠️  发现可能的遥测相关输出:")
        for output in suspicious_output:
            print(f"     {output}")
    else:
        print("✅ 库导入过程中无遥测相关输出")


def main():
    """主函数"""
    print("🔒 遥测禁用验证工具")
    print("=" * 60)

    # 检查环境变量
    env_ok = check_telemetry_disabled()

    # 检查网络连接能力
    check_network_connections()

    # 模拟库导入
    simulate_library_import()

    print("\n" + "=" * 60)

    if env_ok:
        print("🎉 验证完成：遥测功能已完全禁用")
        print("🔒 隐私和数据安全得到充分保护")
        sys.exit(0)
    else:
        print("⚠️  验证失败：存在未禁用的遥测功能")
        print("💡 请运行 ./start_vector_service.sh 或手动设置环境变量")
        sys.exit(1)


if __name__ == "__main__":
    main()
