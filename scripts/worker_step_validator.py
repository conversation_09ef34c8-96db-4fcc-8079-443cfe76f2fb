#!/usr/bin/env python3
"""
Deep Risk RAG Worker Service 分步骤验证工具
专注于真实的文件处理流程：文件→向量化→ChromaDB→检索→风险分析

作者: AI Assistant
日期: 2024-12-19
版本: 3.0 (分步骤版)
"""

import os
import sys
import time
import json
import asyncio
import tempfile
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, NamedTuple
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import (
        Progress,
        SpinnerColumn,
        TextColumn,
        BarColumn,
        TimeElapsedColumn,
    )
    from rich.prompt import Prompt, Confirm
    from rich.status import Status
    from rich import box
    from rich.tree import Tree
    from rich.text import Text

    HAS_RICH = True
except ImportError:
    HAS_RICH = False

try:
    import psutil

    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

try:
    import redis
    from celery import Celery
    from celery.result import AsyncResult
    import httpx
    import chromadb

    HAS_DEPS = True
except ImportError:
    HAS_DEPS = False

# 导入项目模块
try:
    from shared.utils.logger import get_logger
    from shared.redis_config import redis_config
    from shared.models.file_info import FileInfo, FileStatus
    from services.worker_service.config import config as worker_config

    # 重要：使用worker服务的celery应用实例，它包含了所有任务注册
    import services.worker_service.worker  # 这会导入并注册所有任务
    from shared.celery_config import celery_app
    
    # 新增：导入BGE嵌入服务
    from services.worker_service.core.embeddings import WorkerBGEEmbeddingService

    HAS_PROJECT_MODULES = True
except ImportError as e:
    HAS_PROJECT_MODULES = False
    print(f"⚠️ 项目模块导入失败: {e}")


class StepResult(NamedTuple):
    """步骤结果结构"""

    success: bool
    message: str
    details: Dict[str, Any]
    duration: float
    error_code: Optional[str] = None


class ValidationStep(Enum):
    """验证步骤枚举"""

    CONNECTION = "connection"
    VECTORIZATION = "vectorization"
    RETRIEVAL = "retrieval"
    ANALYSIS = "analysis"
    END_TO_END = "end_to_end"


@dataclass
class StepConfig:
    """步骤配置"""

    connection_timeout: float = 5.0
    vectorization_timeout: int = 180
    analysis_timeout: int = 120
    max_retries: int = 3
    retry_delay: float = 2.0
    verbose: bool = True
    cleanup_on_exit: bool = True
    test_file_size: str = "small"  # small, medium, large


class WorkerStepValidator:
    """分步骤 Worker 服务验证器"""

    def __init__(self, config: Optional[StepConfig] = None):
        self.config = config or StepConfig()
        self.console = Console() if HAS_RICH else None
        self.logger = get_logger(__name__) if HAS_PROJECT_MODULES else None

        # 结果存储
        self.step_results: Dict[ValidationStep, StepResult] = {}
        self.test_data = {
            "file_code": None,
            "file_path": None,
            "collection_name": None,
            "task_ids": [],
            "test_files": [],
        }

        self.start_time = datetime.now()
        self._redis_client = None
        self._chroma_client = None
        # 新增：BGE嵌入服务的延迟初始化
        self._embedding_service = None

    def _log(self, message: str, level: str = "info"):
        """统一日志记录"""
        if self.logger:
            getattr(self.logger, level)(message)
        if self.config.verbose:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")

    def _print_rich(self, content, **kwargs):
        """Rich输出包装"""
        if self.console:
            self.console.print(content, **kwargs)
        else:
            print(str(content))

    def print_header(self):
        """打印工具头部信息"""
        if self.console:
            self.console.clear()

        header_text = """
🔧 Deep Risk RAG Worker Service 分步骤验证工具
═══════════════════════════════════════════════════════════════
专注于真实的文件处理流程验证：
📁 文件 → 🔄 向量化 → 💾 ChromaDB → 🔍 检索 → 📊 风险分析
        """

        if self.console:
            info_table = Table(show_header=False, box=box.ROUNDED)
            info_table.add_column("配置项", style="cyan bold")
            info_table.add_column("值", style="white")

            if HAS_PROJECT_MODULES:
                info_table.add_row(
                    "🌐 Redis", f"{redis_config.host}:{redis_config.port}"
                )
                info_table.add_row("🗄️ ChromaDB", worker_config.chroma_url)
                info_table.add_row("🤖 LLM提供商", worker_config.llm_provider)
                info_table.add_row("📦 模型路径", worker_config.model_name)
                info_table.add_row("🔧 设备", worker_config.device)

            info_table.add_row(
                "⏰ 开始时间", self.start_time.strftime("%Y-%m-%d %H:%M:%S")
            )
            info_table.add_row(
                "📋 配置", f"向量化超时: {self.config.vectorization_timeout}s"
            )

            self._print_rich(Panel(header_text, style="bold green"))
            self._print_rich(info_table)
        else:
            print(header_text)
            if HAS_PROJECT_MODULES:
                print(f"🌐 Redis: {redis_config.host}:{redis_config.port}")
                print(f"🗄️ ChromaDB: {worker_config.chroma_url}")
            print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

    async def step_1_connection_test(self) -> StepResult:
        """步骤1: 连接测试"""
        self._log("🔍 执行步骤1: 连接测试")
        start_time = time.time()

        if not HAS_PROJECT_MODULES or not HAS_DEPS:
            return StepResult(
                False,
                "依赖库未正确导入",
                {
                    "missing": (
                        "project_modules" if not HAS_PROJECT_MODULES else "dependencies"
                    )
                },
                time.time() - start_time,
                "DEPENDENCY_ERROR",
            )

        connection_results = {}

        try:
            # 1.1 Redis连接测试
            self._log("  测试Redis连接...")
            redis_result = await self._test_redis_connection()
            connection_results["redis"] = redis_result

            # 1.2 ChromaDB连接测试
            self._log("  测试ChromaDB连接...")
            chroma_result = await self._test_chroma_connection()
            connection_results["chroma"] = chroma_result

            # 1.3 Celery Worker测试
            self._log("  测试Celery Worker...")
            celery_result = await self._test_celery_worker()
            connection_results["celery"] = celery_result

            # 评估整体连接状态
            all_success = all(
                result["success"] for result in connection_results.values()
            )

            if all_success:
                message = "所有连接测试通过"
                self._log("✅ 步骤1完成: 所有连接正常")
            else:
                failed = [
                    name
                    for name, result in connection_results.items()
                    if not result["success"]
                ]
                message = f"连接失败: {', '.join(failed)}"
                self._log(f"❌ 步骤1失败: {message}")

            return StepResult(
                all_success, message, connection_results, time.time() - start_time
            )

        except Exception as e:
            return StepResult(
                False,
                f"连接测试异常: {str(e)}",
                {"error": str(e), "traceback": traceback.format_exc()},
                time.time() - start_time,
                "EXCEPTION",
            )

    async def _test_redis_connection(self) -> Dict[str, Any]:
        """测试Redis连接"""
        try:
            if not self._redis_client:
                self._redis_client = redis_config.client

            # 测试连接
            await asyncio.wait_for(
                asyncio.to_thread(self._redis_client.ping),
                timeout=self.config.connection_timeout,
            )

            # 获取信息
            info = await asyncio.wait_for(
                asyncio.to_thread(self._redis_client.info),
                timeout=self.config.connection_timeout,
            )

            return {
                "success": True,
                "version": info.get("redis_version", "unknown"),
                "memory": info.get("used_memory_human", "unknown"),
                "clients": info.get("connected_clients", 0),
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_chroma_connection(self) -> Dict[str, Any]:
        """测试ChromaDB连接"""
        try:
            if not self._chroma_client:
                self._chroma_client = chromadb.HttpClient(
                    host=worker_config.chroma_host,
                    port=worker_config.chroma_port
                )

            # 测试心跳 - 使用原生客户端方法
            try:
                # 尝试获取版本信息来测试连接
                version_info = self._chroma_client.get_version()
                if version_info:
                    version_str = version_info if isinstance(version_info, str) else str(version_info)
                else:
                    version_str = "connected"
            except Exception as e:
                raise Exception(f"连接失败: {str(e)}")

            return {
                "success": True,
                "status_code": 200,
                "version": version_str,
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_celery_worker(self) -> Dict[str, Any]:
        """测试Celery Worker"""
        try:
            # 详细的Celery诊断
            self._log("    检查Celery配置...")
            broker_url = celery_app.conf.broker_url
            result_backend = celery_app.conf.result_backend
            self._log(f"    Broker URL: {broker_url}")
            self._log(f"    Result Backend: {result_backend}")

            # 检查已注册的任务
            registered_tasks = [
                name
                for name in celery_app.tasks.keys()
                if not name.startswith("celery.")
            ]
            self._log(f"    已注册任务: {registered_tasks}")

            inspect = celery_app.control.inspect()

            # 获取worker状态
            self._log("    检查Worker状态...")
            stats = await asyncio.wait_for(
                asyncio.to_thread(inspect.stats),
                timeout=self.config.connection_timeout * 2,
            )

            if stats:
                worker_count = len(stats)
                worker_names = list(stats.keys())
                self._log(f"    找到 {worker_count} 个Worker: {worker_names}")

                # 检查队列状态
                active_queues = await asyncio.wait_for(
                    asyncio.to_thread(inspect.active_queues), timeout=5
                )
                self._log(f"    活跃队列: {active_queues}")

                # 测试健康检查任务
                try:
                    self._log("    测试健康检查任务...")
                    health_result = celery_app.send_task("worker.tasks.health_check")
                    health_response = await asyncio.wait_for(
                        asyncio.to_thread(health_result.get), timeout=10
                    )
                    health_ok = True
                    self._log("    健康检查任务成功")
                except Exception as health_error:
                    health_ok = False
                    self._log(f"    健康检查任务失败: {health_error}")

                return {
                    "success": True,
                    "worker_count": worker_count,
                    "worker_names": worker_names,
                    "health_check": health_ok,
                    "broker_url": broker_url,
                    "registered_tasks": registered_tasks,
                    "active_queues": active_queues,
                }
            else:
                # 检查是否有进程运行但无法通信
                self._log("    Worker stats返回空，检查进程...")
                import subprocess

                try:
                    result = subprocess.run(
                        ["ps", "aux"], capture_output=True, text=True
                    )
                    celery_processes = [
                        line
                        for line in result.stdout.split("\n")
                        if "celery" in line and "worker" in line
                    ]

                    if celery_processes:
                        self._log(
                            f"    发现 {len(celery_processes)} 个Celery进程但无法通信"
                        )
                        return {
                            "success": False,
                            "error": "Worker进程存在但无法通信 - 可能是配置不匹配",
                            "process_count": len(celery_processes),
                            "broker_url": broker_url,
                            "registered_tasks": registered_tasks,
                        }
                    else:
                        return {
                            "success": False,
                            "error": "No active workers found",
                            "broker_url": broker_url,
                            "registered_tasks": registered_tasks,
                        }
                except Exception as ps_error:
                    return {
                        "success": False,
                        "error": f"No active workers found (ps检查失败: {ps_error})",
                        "broker_url": broker_url,
                        "registered_tasks": registered_tasks,
                    }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "broker_url": getattr(celery_app.conf, "broker_url", "unknown"),
            }

    async def step_2_vectorization_test(self) -> StepResult:
        """步骤2: 文件向量化测试"""
        self._log("🔍 执行步骤2: 文件向量化测试")
        start_time = time.time()

        if not HAS_PROJECT_MODULES or not HAS_DEPS:
            return StepResult(
                False,
                "依赖库未正确导入",
                {
                    "missing": (
                        "project_modules" if not HAS_PROJECT_MODULES else "dependencies"
                    )
                },
                time.time() - start_time,
                "DEPENDENCY_ERROR",
            )

        vectorization_results = {}

        try:
            # 2.1 获取用户指定的文件
            self._log("  获取用户指定的测试文件...")
            test_file_info = await self._get_user_test_file()
            if not test_file_info:
                return StepResult(
                    False,
                    "用户取消或未选择有效文件",
                    {"reason": "user_cancelled"},
                    time.time() - start_time,
                    "USER_CANCELLED",
                )

            vectorization_results["file_info"] = test_file_info

            # 2.2 验证文件
            self._log(f"  验证文件: {test_file_info['file_name']}")
            file_validation = await self._validate_file_integrity(
                test_file_info["file_path"]
            )
            vectorization_results["file_validation"] = file_validation

            if not file_validation["valid"]:
                return StepResult(
                    False,
                    f"文件验证失败: {file_validation['error']}",
                    vectorization_results,
                    time.time() - start_time,
                    "FILE_VALIDATION_ERROR",
                )

            # 2.3 提交向量化任务
            self._log(f"  提交向量化任务: {test_file_info['file_code']}")
            vectorization_result = await self._submit_real_vectorization_task(
                test_file_info
            )
            vectorization_results["vectorization"] = vectorization_result

            if not vectorization_result["success"]:
                return StepResult(
                    False,
                    f"向量化任务失败: {vectorization_result['error']}",
                    vectorization_results,
                    time.time() - start_time,
                    vectorization_result.get("error_code", "VECTORIZATION_FAILED"),
                )

            # 2.4 验证ChromaDB存储
            self._log("  验证ChromaDB存储...")
            storage_result = await self._verify_chroma_storage(
                test_file_info["file_code"]
            )
            vectorization_results["storage_verification"] = storage_result

            # 2.5 测试向量检索
            self._log("  测试向量检索...")
            retrieval_result = await self._test_vector_retrieval(
                test_file_info["file_code"],
                self._generate_test_query_from_file(test_file_info),
            )
            vectorization_results["retrieval_test"] = retrieval_result

            # 评估整体结果
            all_success = (
                vectorization_result["success"]
                and storage_result["success"]
                and retrieval_result["success"]
            )

            if all_success:
                message = f"文件 '{test_file_info['file_name']}' 向量化测试完成"
                self._log("✅ 步骤2完成: 文件向量化测试成功")
            else:
                failed_parts = []
                if not vectorization_result["success"]:
                    failed_parts.append("向量化")
                if not storage_result["success"]:
                    failed_parts.append("存储验证")
                if not retrieval_result["success"]:
                    failed_parts.append("检索测试")

                message = f"向量化测试部分失败: {', '.join(failed_parts)}"
                self._log(f"⚠️ 步骤2部分成功: {message}")

            # 保存测试数据供后续步骤使用
            self.test_data.update(
                {
                    "test_file": test_file_info,
                    "vectorization_results": vectorization_results,
                }
            )

            return StepResult(
                all_success, message, vectorization_results, time.time() - start_time
            )

        except Exception as e:
            # 使用增强的错误处理
            error_details = await self._enhanced_error_handling("文件向量化测试", e)

            return StepResult(
                False,
                f"向量化测试异常: {str(e)}",
                {
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                    "error_analysis": error_details,
                },
                time.time() - start_time,
                error_details["error_code"],
            )

    async def _get_user_test_file(self) -> Optional[Dict[str, str]]:
        """获取用户指定的测试文件"""
        if self.console:
            self._print_rich("\n[bold cyan]📁 选择测试文件[/bold cyan]")
            self._print_rich("请选择文件来源:")
            self._print_rich("1. 输入文件路径")
            self._print_rich("2. 从项目data目录选择")
            self._print_rich("3. 取消测试")

            choice = Prompt.ask("请选择", choices=["1", "2", "3"], default="1")
        else:
            print("\n📁 选择测试文件")
            print("1. 输入文件路径")
            print("2. 从项目data目录选择")
            print("3. 取消测试")
            choice = input("请选择 (1-3, 默认 1): ").strip() or "1"

        if choice == "3":
            self._log("用户取消文件选择")
            return None
        elif choice == "1":
            return await self._get_file_by_path()
        elif choice == "2":
            return await self._get_file_from_data_dir()
        else:
            self._log("无效选择，取消测试")
            return None

    async def _get_file_by_path(self) -> Optional[Dict[str, str]]:
        """通过路径获取文件"""
        try:
            if self.console:
                file_path = Prompt.ask("请输入文件路径")
            else:
                file_path = input("请输入文件路径: ").strip()

            if not file_path:
                self._log("未输入文件路径")
                return None

            # 处理相对路径
            path = Path(file_path)
            if not path.is_absolute():
                # 尝试相对于项目根目录
                path = Path(project_root) / file_path

            if not path.exists():
                self._log(f"文件不存在: {file_path}")
                return None

            if not path.is_file():
                self._log(f"路径不是文件: {file_path}")
                return None

            # 检查文件类型
            allowed_extensions = {
                ".txt",
                ".csv",
                ".xlsx",
                ".xls",
                ".pdf",
                ".docx",
                ".doc",
            }
            if path.suffix.lower() not in allowed_extensions:
                self._log(f"不支持的文件类型: {path.suffix}")
                self._log(f"支持的文件类型: {', '.join(allowed_extensions)}")
                return None

            # 生成文件编码
            timestamp = int(time.time())
            file_code = f"test_{path.stem}_{timestamp}"

            file_info = {
                "file_path": str(path),
                "file_name": path.name,
                "file_code": file_code,
                "file_type": path.suffix.lower(),
                "file_size": path.stat().st_size,
            }

            self._log(f"选择文件: {path.name} ({file_info['file_size']} 字节)")
            return file_info

        except Exception as e:
            self._log(f"获取文件路径失败: {e}", "error")
            return None

    async def _get_file_from_data_dir(self) -> Optional[Dict[str, str]]:
        """从data目录选择文件"""
        try:
            data_dir = Path(project_root) / "data"
            if not data_dir.exists():
                self._log("data目录不存在，请使用路径输入方式")
                return await self._get_file_by_path()

            # 查找支持的文件
            allowed_extensions = {
                ".txt",
                ".csv",
                ".xlsx",
                ".xls",
                ".pdf",
                ".docx",
                ".doc",
            }
            files = []

            for pattern in [
                "**/*.txt",
                "**/*.csv",
                "**/*.xlsx",
                "**/*.xls",
                "**/*.pdf",
                "**/*.docx",
                "**/*.doc",
            ]:
                files.extend(data_dir.glob(pattern))

            if not files:
                self._log("data目录中没有找到支持的文件")
                return await self._get_file_by_path()

            # 显示文件列表
            self._log(f"找到 {len(files)} 个文件:")

            if self.console:
                # 创建文件选择表格
                table = Table(title="可用文件", box=box.ROUNDED)
                table.add_column("序号", style="cyan", justify="center")
                table.add_column("文件名", style="white")
                table.add_column("类型", style="yellow")
                table.add_column("大小", style="green")
                table.add_column("路径", style="dim")

                for i, file_path in enumerate(files[:20], 1):  # 最多显示20个
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    table.add_row(
                        str(i),
                        file_path.name,
                        file_path.suffix.upper(),
                        f"{size_mb:.2f}MB",
                        str(file_path.relative_to(project_root)),
                    )

                if len(files) > 20:
                    table.add_row("...", f"还有 {len(files) - 20} 个文件", "", "", "")

                self._print_rich(table)

                try:
                    choice = int(
                        Prompt.ask(
                            f"请选择文件序号 (1-{min(len(files), 20)})", default="1"
                        )
                    )
                except ValueError:
                    self._log("无效的序号")
                    return None
            else:
                for i, file_path in enumerate(files[:20], 1):
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    print(
                        f"{i:2}. {file_path.name} ({file_path.suffix.upper()}, {size_mb:.2f}MB)"
                    )

                if len(files) > 20:
                    print(f"... 还有 {len(files) - 20} 个文件")

                try:
                    choice = int(
                        input(f"请选择文件序号 (1-{min(len(files), 20)}): ") or "1"
                    )
                except ValueError:
                    self._log("无效的序号")
                    return None

            if choice < 1 or choice > min(len(files), 20):
                self._log("序号超出范围")
                return None

            selected_file = files[choice - 1]

            # 生成文件编码
            timestamp = int(time.time())
            file_code = f"test_{selected_file.stem}_{timestamp}"

            file_info = {
                "file_path": str(selected_file),
                "file_name": selected_file.name,
                "file_code": file_code,
                "file_type": selected_file.suffix.lower(),
                "file_size": selected_file.stat().st_size,
            }

            self._log(f"选择文件: {selected_file.name}")
            return file_info

        except Exception as e:
            self._log(f"从data目录选择文件失败: {e}", "error")
            return None

    async def _submit_real_vectorization_task(
        self, file_info: Dict[str, str]
    ) -> Dict[str, Any]:
        """提交真实的向量化任务"""
        try:
            # 检查已注册的任务
            self._log(f"    检查已注册的任务...")
            registered_tasks = [
                name
                for name in celery_app.tasks.keys()
                if not name.startswith("celery.")
            ]
            self._log(f"    已注册任务: {registered_tasks}")

            if "worker.tasks.vectorize_file" not in celery_app.tasks:
                return {
                    "success": False,
                    "error": "worker.tasks.vectorize_file任务未注册",
                    "error_code": "TASK_NOT_REGISTERED",
                    "registered_tasks": registered_tasks,
                    "file_code": file_info["file_code"],
                }

            # 创建FileInfo对象
            file_info_obj = FileInfo(
                file_code=file_info["file_code"],
                file_name=file_info["file_name"],
                file_path=file_info["file_path"],
                file_size=file_info["file_size"],
                file_type=file_info["file_type"],
                status=FileStatus.UPLOADING,
            )

            # 提交向量化任务
            self._log(f"    提交向量化任务: {file_info['file_code']}")
            self._log(f"    任务名称: worker.tasks.vectorize_file")
            self._log(f"    Celery配置: {celery_app.conf.broker_url}")

            task_result = celery_app.send_task(
                "worker.tasks.vectorize_file",
                args=[
                    file_info["file_code"],
                    file_info["file_path"],
                    file_info_obj.model_dump(),
                ],
                # 不指定队列，让任务路由自动处理
            )

            # 监控任务进度
            result_details = await self._monitor_task_progress(
                task_result, file_info["file_code"], f"文件({file_info['file_name']})"
            )

            # 保存任务ID
            if "task_ids" not in self.test_data:
                self.test_data["task_ids"] = []
            self.test_data["task_ids"].append(task_result.id)

            return result_details

        except Exception as e:
            error_code = self._classify_error(e)
            return {
                "success": False,
                "error": str(e),
                "error_code": error_code,
                "file_code": file_info["file_code"],
            }

    async def _monitor_task_progress(
        self, task_result, file_code: str, file_type: str
    ) -> Dict[str, Any]:
        """监控任务进度"""
        start_time = time.time()
        last_progress = -1

        try:
            # 等待任务完成，最多等待配置的超时时间
            while (
                not task_result.ready()
                and time.time() - start_time < self.config.vectorization_timeout
            ):
                # 获取任务状态
                if hasattr(task_result, "state") and hasattr(task_result, "info"):
                    state = task_result.state
                    info = task_result.info or {}

                    if isinstance(info, dict):
                        progress = info.get("progress", 0)
                        stage = info.get("stage", "未知")

                        # 只在进度有变化时输出
                        if progress != last_progress:
                            self._log(f"    {file_type}进度: {progress}% - {stage}")
                            last_progress = progress

                await asyncio.sleep(2)

            # 检查任务结果
            if task_result.ready():
                if task_result.successful():
                    result_data = task_result.get()
                    duration = time.time() - start_time

                    self._log(f"    {file_type}向量化成功，耗时 {duration:.2f}s")

                    return {
                        "success": True,
                        "task_id": task_result.id,
                        "file_code": file_code,
                        "document_count": result_data.get("document_count", 0),
                        "processing_time": result_data.get("processing_time", duration),
                        "parse_time": result_data.get("parse_time", 0),
                        "vector_time": result_data.get("vector_time", 0),
                        "message": result_data.get("message", "向量化完成"),
                        "total_duration": duration,
                    }
                else:
                    error_msg = str(task_result.result)
                    self._log(f"    {file_type}向量化失败: {error_msg}")

                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_result.id,
                        "file_code": file_code,
                        "total_duration": time.time() - start_time,
                    }
            else:
                timeout_msg = f"向量化任务超时 ({self.config.vectorization_timeout}s)"
                self._log(f"    {file_type}{timeout_msg}")

                return {
                    "success": False,
                    "error": timeout_msg,
                    "task_id": task_result.id,
                    "file_code": file_code,
                    "total_duration": time.time() - start_time,
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"监控任务进度异常: {str(e)}",
                "task_id": task_result.id,
                "file_code": file_code,
                "total_duration": time.time() - start_time,
            }

    def _classify_error(self, error: Exception) -> str:
        """分类错误类型"""
        error_str = str(error).lower()

        if "timeout" in error_str or "time out" in error_str:
            return "TIMEOUT"
        elif "connection" in error_str or "connect" in error_str:
            return "CONNECTION_ERROR"
        elif "permission" in error_str or "denied" in error_str:
            return "PERMISSION_ERROR"
        elif "not found" in error_str or "404" in error_str:
            return "NOT_FOUND"
        elif "worker" in error_str and "active" in error_str:
            return "WORKER_NOT_AVAILABLE"
        elif "chroma" in error_str or "collection" in error_str:
            return "CHROMA_ERROR"
        elif "celery" in error_str or "broker" in error_str:
            return "CELERY_ERROR"
        elif "memory" in error_str or "resource" in error_str:
            return "RESOURCE_ERROR"
        else:
            return "UNKNOWN_ERROR"

    def _generate_test_query_from_file(self, file_info: Dict[str, str]) -> str:
        """根据文件类型生成测试查询"""
        file_type = file_info["file_type"].lower()
        file_name = file_info["file_name"]

        if file_type in [".csv", ".xlsx", ".xls"]:
            return "风险数据分析"
        elif file_type in [".txt", ".md"]:
            return "文档内容查询"
        elif file_type in [".pdf", ".docx", ".doc"]:
            return "文档关键信息"
        else:
            return f"{file_name}内容检索"

    async def _validate_file_integrity(self, file_path: str) -> Dict[str, Any]:
        """验证文件完整性"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {"valid": False, "error": "文件不存在"}

            # 基本文件信息
            stat = path.stat()
            file_info = {
                "valid": True,
                "size": stat.st_size,
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "readable": path.is_file() and os.access(path, os.R_OK),
            }

            # 尝试读取文件内容
            try:
                content = path.read_text(encoding="utf-8")
                file_info.update(
                    {
                        "encoding": "utf-8",
                        "line_count": len(content.splitlines()),
                        "char_count": len(content),
                        "has_chinese": any(
                            "\u4e00" <= char <= "\u9fff" for char in content[:100]
                        ),
                    }
                )
            except UnicodeDecodeError:
                file_info["encoding_error"] = "非UTF-8编码或包含无效字符"

            return file_info

        except Exception as e:
            return {"valid": False, "error": str(e)}

    async def _enhanced_error_handling(
        self, operation: str, error: Exception
    ) -> Dict[str, Any]:
        """增强的错误处理"""
        error_info = {
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "error_code": self._classify_error(error),
            "timestamp": datetime.now().isoformat(),
            "system_metrics": await self._get_system_metrics(),
        }

        # 添加特定错误的建议
        suggestions = {
            "TIMEOUT": ["检查网络连接", "增加超时时间", "确认服务正常运行"],
            "CONNECTION_ERROR": ["验证服务地址", "检查端口是否开放", "确认服务已启动"],
            "WORKER_NOT_AVAILABLE": [
                "启动Celery Worker",
                "检查Redis连接",
                "验证队列配置",
            ],
            "CHROMA_ERROR": ["检查ChromaDB服务", "验证API端点", "确认集合权限"],
            "PERMISSION_ERROR": ["检查文件权限", "确认目录可写", "验证用户权限"],
            "RESOURCE_ERROR": ["检查内存使用", "清理临时文件", "重启相关服务"],
        }

        error_info["suggestions"] = suggestions.get(
            error_info["error_code"], ["检查错误日志", "联系技术支持"]
        )

        return error_info

    async def _get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version.split()[0],
        }

        if HAS_PSUTIL:
            try:
                # CPU和内存使用率
                metrics.update(
                    {
                        "cpu_percent": psutil.cpu_percent(interval=0.1),
                        "memory_percent": psutil.virtual_memory().percent,
                        "available_memory_gb": psutil.virtual_memory().available
                        / (1024**3),
                        "disk_usage_percent": (
                            psutil.disk_usage("/").percent
                            if sys.platform != "win32"
                            else psutil.disk_usage("C:\\").percent
                        ),
                    }
                )
            except Exception as e:
                metrics["psutil_error"] = str(e)

        return metrics

    async def _verify_chroma_storage(self, file_code: str) -> Dict[str, Any]:
        """验证ChromaDB存储"""
        try:
            if not self._chroma_client:
                self._chroma_client = chromadb.HttpClient(
                    host=worker_config.chroma_host,
                    port=worker_config.chroma_port
                )

            # 构建集合名称
            collection_name = f"{worker_config.chroma_collection_prefix}{file_code}"

            # 检查集合是否存在
            try:
                collection = self._chroma_client.get_collection(name=collection_name)
                collection_exists = True

                # 获取集合信息
                document_count = collection.count()

                return {
                    "success": True,
                    "collection_name": collection_name,
                    "collection_exists": True,
                    "document_count": document_count,
                    "collection_info": {
                        "name": collection.name,
                        "metadata": collection.metadata
                    },
                }
            except Exception:
                # 集合不存在
                return {
                    "success": False,
                    "error": "集合不存在",
                    "collection_name": collection_name,
                    "collection_exists": False,
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"验证ChromaDB存储异常: {str(e)}",
                "collection_name": f"{worker_config.chroma_collection_prefix}{file_code}",
            }

    def _get_embedding_service(self):
        """延迟初始化BGE嵌入服务"""
        if self._embedding_service is None and HAS_PROJECT_MODULES:
            try:
                self._log("  初始化BGE-M3嵌入服务...")
                self._embedding_service = WorkerBGEEmbeddingService(
                    model_name=worker_config.model_name,
                    device=worker_config.device,
                    batch_size=worker_config.batch_size,
                    max_length=worker_config.max_length,
                    normalize_embeddings=worker_config.normalize_embeddings,
                    enable_cache=False,  # 测试中不使用缓存
                    auto_cleanup=worker_config.auto_cleanup,
                    memory_monitor=worker_config.enable_memory_monitoring,
                )
                self._log("  ✅ BGE-M3嵌入服务初始化成功")
            except Exception as e:
                self._log(f"  ❌ BGE-M3嵌入服务初始化失败: {e}", "error")
                self._embedding_service = None
        return self._embedding_service

    async def _test_vector_retrieval(
        self, file_code: str, query: str
    ) -> Dict[str, Any]:
        """测试向量检索 - 使用BGE-M3嵌入服务避免默认模型下载"""
        try:
            if not self._chroma_client:
                self._chroma_client = chromadb.HttpClient(
                    host=worker_config.chroma_host,
                    port=worker_config.chroma_port
                )

            # 构建集合名称
            collection_name = f"{worker_config.chroma_collection_prefix}{file_code}"

            # 获取集合并执行查询
            try:
                collection = self._chroma_client.get_collection(name=collection_name)

                # 获取BGE嵌入服务生成查询向量（保持维度一致性）
                embedding_service = self._get_embedding_service()
                if not embedding_service:
                    return {
                        "success": False,
                        "error": "BGE嵌入服务初始化失败，无法进行检索测试",
                        "collection_name": collection_name,
                        "query": query,
                    }

                # 使用BGE-M3生成查询嵌入向量（1024维）
                self._log(f"  使用BGE-M3生成查询嵌入: {query[:30]}...")
                try:
                    query_embedding = embedding_service.embed_query(query)
                    self._log(f"  查询嵌入生成成功，维度: {len(query_embedding)}")
                except Exception as embed_error:
                    return {
                        "success": False,
                        "error": f"查询嵌入生成失败: {str(embed_error)}",
                        "collection_name": collection_name,
                        "query": query,
                    }

                # 执行查询 - 使用BGE-M3生成的1024维嵌入向量
                self._log(f"  执行向量检索查询...")
                try:
                    results = collection.query(
                        query_embeddings=[query_embedding],  # 使用BGE-M3生成的嵌入
                        n_results=3,
                        include=["documents", "distances", "metadatas"]
                    )
                    self._log(f"  查询执行成功")
                except Exception as query_error:
                    return {
                        "success": False,
                        "error": f"ChromaDB查询失败: {str(query_error)}",
                        "collection_name": collection_name,
                        "query": query,
                    }

                # 解析结果
                documents = results.get("documents", [[]])[0] if results.get("documents") else []
                distances = results.get("distances", [[]])[0] if results.get("distances") else []
                metadatas = results.get("metadatas", [[]])[0] if results.get("metadatas") else []

                return {
                    "success": True,
                    "collection_name": collection_name,
                    "query": query,
                    "query_embedding_dim": len(query_embedding),
                    "results_count": len(documents),
                    "documents": documents,
                    "distances": distances,
                    "metadatas": metadatas,
                    "min_distance": min(distances) if distances else None,
                    "max_distance": max(distances) if distances else None,
                    "embedding_service": "BGE-M3",
                }

            except Exception as collection_error:
                return {
                    "success": False,
                    "error": f"集合查询失败: {str(collection_error)}",
                    "collection_name": collection_name,
                    "query": query,
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"向量检索异常: {str(e)}",
                "collection_name": f"{worker_config.chroma_collection_prefix}{file_code}",
                "query": query,
            }

    async def _get_available_file_codes(self) -> Dict[str, Any]:
        """获取可用的文件编码列表"""
        try:
            if not self._chroma_client:
                self._chroma_client = chromadb.HttpClient(
                    host=worker_config.chroma_host,
                    port=worker_config.chroma_port
                )

            # 获取所有集合
            collections = self._chroma_client.list_collections()

            # 提取文件编码（去掉前缀）
            file_codes = []
            for collection in collections:
                collection_name = collection.name
                if collection_name.startswith(worker_config.chroma_collection_prefix):
                    file_code = collection_name[len(worker_config.chroma_collection_prefix):]

                    # 获取集合信息
                    try:
                        collection_obj = self._chroma_client.get_collection(name=collection_name)
                        document_count = collection_obj.count()

                        file_codes.append({
                            "file_code": file_code,
                            "collection_name": collection_name,
                            "document_count": document_count,
                            "metadata": collection.metadata or {}
                        })
                    except Exception as e:
                        self._log(f"  获取集合 {collection_name} 信息失败: {e}", "warning")
                        file_codes.append({
                            "file_code": file_code,
                            "collection_name": collection_name,
                            "document_count": 0,
                            "metadata": {},
                            "error": str(e)
                        })

            return {
                "success": True,
                "file_codes": file_codes,
                "total_count": len(file_codes)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取文件编码列表失败: {str(e)}",
                "file_codes": [],
                "total_count": 0
            }

    async def step_4_analysis_test(self) -> StepResult:
        """步骤4: 风险分析测试"""
        self._log("🔍 执行步骤4: 风险分析测试")
        start_time = time.time()

        if not HAS_PROJECT_MODULES or not HAS_DEPS:
            return StepResult(
                False,
                "依赖库未正确导入",
                {
                    "missing": (
                        "project_modules" if not HAS_PROJECT_MODULES else "dependencies"
                    )
                },
                time.time() - start_time,
                "DEPENDENCY_ERROR",
            )

        analysis_results = {}

        try:
            # 4.1 获取可用文件编码列表
            self._log("  获取可用文件编码列表...")
            file_codes_result = await self._get_available_file_codes()
            analysis_results["file_codes_query"] = file_codes_result

            if not file_codes_result["success"]:
                return StepResult(
                    False,
                    f"获取文件编码列表失败: {file_codes_result['error']}",
                    analysis_results,
                    time.time() - start_time,
                    "FILE_CODES_QUERY_FAILED",
                )

            if not file_codes_result["file_codes"]:
                return StepResult(
                    False,
                    "没有找到可用的文件编码，请先执行步骤2进行文件向量化",
                    analysis_results,
                    time.time() - start_time,
                    "NO_FILE_CODES_AVAILABLE",
                )

            # 4.2 选择文件编码进行分析
            self._log("  选择文件编码进行分析...")
            selected_file_code = await self._select_file_code_for_analysis(
                file_codes_result["file_codes"]
            )

            if not selected_file_code:
                return StepResult(
                    False,
                    "用户取消或未选择有效文件编码",
                    analysis_results,
                    time.time() - start_time,
                    "USER_CANCELLED",
                )

            analysis_results["selected_file_code"] = selected_file_code

            # 4.3 提交风险分析任务
            self._log(f"  提交风险分析任务: {selected_file_code['file_code']}")
            analysis_task_result = await self._submit_analysis_task(selected_file_code)
            analysis_results["analysis_task"] = analysis_task_result

            if not analysis_task_result["success"]:
                return StepResult(
                    False,
                    f"风险分析任务失败: {analysis_task_result['error']}",
                    analysis_results,
                    time.time() - start_time,
                    analysis_task_result.get("error_code", "ANALYSIS_TASK_FAILED"),
                )

            # 4.4 验证分析结果
            self._log("  验证分析结果...")
            result_validation = await self._validate_analysis_result(
                analysis_task_result["result"]
            )
            analysis_results["result_validation"] = result_validation

            # 评估整体结果
            all_success = (
                file_codes_result["success"]
                and analysis_task_result["success"]
                and result_validation["success"]
            )

            if all_success:
                message = f"文件 '{selected_file_code['file_code']}' 风险分析测试完成"
                self._log("✅ 步骤4完成: 风险分析测试成功")
            else:
                failed_parts = []
                if not file_codes_result["success"]:
                    failed_parts.append("文件编码查询")
                if not analysis_task_result["success"]:
                    failed_parts.append("分析任务")
                if not result_validation["success"]:
                    failed_parts.append("结果验证")

                message = f"风险分析测试部分失败: {', '.join(failed_parts)}"
                self._log(f"⚠️ 步骤4部分成功: {message}")

            # 保存测试数据供后续步骤使用
            self.test_data.update(
                {
                    "analysis_test_results": analysis_results,
                }
            )

            return StepResult(
                all_success, message, analysis_results, time.time() - start_time
            )

        except Exception as e:
            # 使用增强的错误处理
            error_details = await self._enhanced_error_handling("风险分析测试", e)

            return StepResult(
                False,
                f"风险分析测试异常: {str(e)}",
                {
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                    "error_analysis": error_details,
                },
                time.time() - start_time,
                error_details["error_code"],
            )

    async def _select_file_code_for_analysis(
        self, file_codes: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """选择文件编码进行分析"""
        if not file_codes:
            return None

        # 如果只有一个文件编码，直接使用
        if len(file_codes) == 1:
            selected = file_codes[0]
            self._log(f"自动选择唯一文件编码: {selected['file_code']}")
            return selected

        # 如果有之前步骤的测试文件，优先使用
        if hasattr(self, 'test_data') and 'test_file' in self.test_data:
            test_file_code = self.test_data['test_file']['file_code']
            for file_code_info in file_codes:
                if file_code_info['file_code'] == test_file_code:
                    self._log(f"使用步骤2的测试文件: {test_file_code}")
                    return file_code_info

        # 显示文件编码列表供用户选择
        if self.console:
            self._print_rich("\n[bold cyan]📋 选择文件编码进行风险分析[/bold cyan]")

            # 创建文件编码选择表格
            table = Table(title="可用文件编码", box=box.ROUNDED)
            table.add_column("序号", style="cyan", justify="center")
            table.add_column("文件编码", style="white")
            table.add_column("文档数量", style="yellow")
            table.add_column("集合名称", style="dim")

            for i, file_info in enumerate(file_codes[:20], 1):  # 最多显示20个
                table.add_row(
                    str(i),
                    file_info['file_code'],
                    str(file_info['document_count']),
                    file_info['collection_name']
                )

            if len(file_codes) > 20:
                table.add_row("...", f"还有 {len(file_codes) - 20} 个文件", "", "")

            self._print_rich(table)

            try:
                choice = int(
                    Prompt.ask(
                        f"请选择文件编码序号 (1-{min(len(file_codes), 20)})", default="1"
                    )
                )
            except ValueError:
                self._log("无效的序号")
                return None
        else:
            print("\n📋 选择文件编码进行风险分析")
            for i, file_info in enumerate(file_codes[:20], 1):
                print(
                    f"{i:2}. {file_info['file_code']} "
                    f"(文档数: {file_info['document_count']})"
                )

            if len(file_codes) > 20:
                print(f"... 还有 {len(file_codes) - 20} 个文件")

            try:
                choice = int(
                    input(f"请选择文件编码序号 (1-{min(len(file_codes), 20)}): ") or "1"
                )
            except ValueError:
                self._log("无效的序号")
                return None

        if choice < 1 or choice > min(len(file_codes), 20):
            self._log("序号超出范围")
            return None

        selected = file_codes[choice - 1]
        self._log(f"选择文件编码: {selected['file_code']}")
        return selected

    async def _submit_analysis_task(
        self, file_code_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提交风险分析任务"""
        try:
            file_code = file_code_info["file_code"]

            # 检查已注册的任务
            self._log(f"    检查已注册的分析任务...")
            registered_tasks = [
                name
                for name in celery_app.tasks.keys()
                if not name.startswith("celery.")
            ]
            self._log(f"    已注册任务: {registered_tasks}")

            if "worker.tasks.analyze_risk" not in celery_app.tasks:
                return {
                    "success": False,
                    "error": "worker.tasks.analyze_risk任务未注册",
                    "error_code": "TASK_NOT_REGISTERED",
                    "registered_tasks": registered_tasks,
                    "file_code": file_code,
                }

            # 提交分析任务
            self._log(f"    提交风险分析任务: {file_code}")
            self._log(f"    任务名称: worker.tasks.analyze_risk")
            self._log(f"    Celery配置: {celery_app.conf.broker_url}")

            task_result = celery_app.send_task(
                "worker.tasks.analyze_risk",
                args=[
                    file_code,
                    "default",  # analysis_type
                    None,       # options
                    1,          # priority
                ],
            )

            # 监控任务进度
            result_details = await self._monitor_analysis_progress(
                task_result, file_code, f"文件({file_code})"
            )

            # 保存任务ID
            if "task_ids" not in self.test_data:
                self.test_data["task_ids"] = []
            self.test_data["task_ids"].append(task_result.id)

            return result_details

        except Exception as e:
            error_code = self._classify_error(e)
            return {
                "success": False,
                "error": str(e),
                "error_code": error_code,
                "file_code": file_code_info["file_code"],
            }

    async def _monitor_analysis_progress(
        self, task_result, file_code: str, file_type: str
    ) -> Dict[str, Any]:
        """监控分析任务进度"""
        start_time = time.time()
        last_progress = -1

        try:
            # 等待任务完成，最多等待配置的超时时间
            while (
                not task_result.ready()
                and time.time() - start_time < self.config.analysis_timeout
            ):
                # 获取任务状态
                if hasattr(task_result, "state") and hasattr(task_result, "info"):
                    state = task_result.state
                    info = task_result.info or {}

                    if isinstance(info, dict):
                        progress = info.get("progress", 0)
                        stage = info.get("stage", "未知")

                        # 只在进度有变化时输出
                        if progress != last_progress:
                            self._log(f"    {file_type}分析进度: {progress}% - {stage}")
                            last_progress = progress

                await asyncio.sleep(2)

            # 检查任务结果
            if task_result.ready():
                if task_result.successful():
                    result_data = task_result.get()
                    duration = time.time() - start_time

                    self._log(f"    {file_type}风险分析成功，耗时 {duration:.2f}s")

                    return {
                        "success": True,
                        "task_id": task_result.id,
                        "file_code": file_code,
                        "result": result_data,
                        "total_duration": duration,
                    }
                else:
                    error_msg = str(task_result.result)
                    self._log(f"    {file_type}风险分析失败: {error_msg}")

                    return {
                        "success": False,
                        "error": error_msg,
                        "task_id": task_result.id,
                        "file_code": file_code,
                        "total_duration": time.time() - start_time,
                    }
            else:
                timeout_msg = f"风险分析任务超时 ({self.config.analysis_timeout}s)"
                self._log(f"    {file_type}{timeout_msg}")

                return {
                    "success": False,
                    "error": timeout_msg,
                    "task_id": task_result.id,
                    "file_code": file_code,
                    "total_duration": time.time() - start_time,
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"监控分析任务进度异常: {str(e)}",
                "task_id": task_result.id,
                "file_code": file_code,
                "total_duration": time.time() - start_time,
            }

    async def _validate_analysis_result(self, result_data: Any) -> Dict[str, Any]:
        """验证分析结果"""
        try:
            validation_results = {
                "success": True,
                "checks": {},
                "warnings": [],
                "errors": []
            }

            # 检查结果数据类型
            if not isinstance(result_data, dict):
                validation_results["errors"].append(f"结果数据类型错误: {type(result_data)}")
                validation_results["success"] = False
                return validation_results

            validation_results["checks"]["data_type"] = "dict ✓"

            # 检查必要字段
            required_fields = ["status", "file_code", "analysis_id"]
            for field in required_fields:
                if field in result_data:
                    validation_results["checks"][f"field_{field}"] = "存在 ✓"
                else:
                    validation_results["errors"].append(f"缺少必要字段: {field}")
                    validation_results["success"] = False

            # 检查状态字段
            if "status" in result_data:
                status = result_data["status"]
                if status == "completed":
                    validation_results["checks"]["status"] = "completed ✓"
                elif status == "failed":
                    validation_results["warnings"].append("分析状态为失败")
                    validation_results["checks"]["status"] = "failed ⚠️"
                else:
                    validation_results["warnings"].append(f"未知状态: {status}")
                    validation_results["checks"]["status"] = f"{status} ⚠️"

            # 检查分析内容 - 修正字段名为 analysis_summary
            if "analysis_summary" in result_data:
                content = result_data["analysis_summary"]
                if isinstance(content, str) and len(content) > 0:
                    validation_results["checks"]["analysis_summary"] = f"长度 {len(content)} ✓"

                    # 检查是否包含关键分析要素
                    content_lower = content.lower()
                    key_elements = ["违约", "风险", "概率", "分析"]
                    found_elements = [elem for elem in key_elements if elem in content_lower]

                    if found_elements:
                        validation_results["checks"]["key_elements"] = f"包含 {found_elements} ✓"
                    else:
                        validation_results["warnings"].append("分析内容可能缺少关键要素")
                else:
                    validation_results["errors"].append("分析内容为空或格式错误")
                    validation_results["success"] = False
            else:
                # 兼容性检查：也检查旧的 analysis_content 字段
                if "analysis_content" in result_data:
                    content = result_data["analysis_content"]
                    if isinstance(content, str) and len(content) > 0:
                        validation_results["checks"]["analysis_content"] = f"长度 {len(content)} ✓ (旧字段)"
                        validation_results["warnings"].append("使用了旧的 analysis_content 字段，建议升级到 analysis_summary")
                    else:
                        validation_results["errors"].append("分析内容为空或格式错误")
                        validation_results["success"] = False
                else:
                    validation_results["errors"].append("缺少分析内容 (analysis_summary 或 analysis_content)")
                    validation_results["success"] = False

            # 检查处理时间
            if "processing_time" in result_data:
                processing_time = result_data["processing_time"]
                if isinstance(processing_time, (int, float)) and processing_time > 0:
                    validation_results["checks"]["processing_time"] = f"{processing_time:.2f}s ✓"
                else:
                    validation_results["warnings"].append("处理时间数据异常")

            # 检查文档数量
            if "document_count" in result_data:
                doc_count = result_data["document_count"]
                if isinstance(doc_count, int) and doc_count > 0:
                    validation_results["checks"]["document_count"] = f"{doc_count} 个文档 ✓"
                else:
                    validation_results["warnings"].append("文档数量数据异常")

            # 检查错误信息
            if "error_message" in result_data and result_data["error_message"]:
                validation_results["warnings"].append(f"包含错误信息: {result_data['error_message']}")

            return validation_results

        except Exception as e:
            return {
                "success": False,
                "error": f"验证分析结果异常: {str(e)}",
                "checks": {},
                "warnings": [],
                "errors": [str(e)]
            }

    def show_step_menu(self) -> str:
        """显示步骤菜单"""
        if self.console:
            self._print_rich("[bold cyan]选择验证步骤:[/bold cyan]")

            # 创建步骤状态树
            tree = Tree("🔧 验证步骤")

            steps = [
                ("1", "连接测试", "测试Redis、ChromaDB、Celery连接"),
                ("2", "文件向量化", "测试文件向量化到ChromaDB"),
                ("3", "向量检索", "验证ChromaDB数据检索"),
                ("4", "风险分析", "测试基于向量的风险分析"),
                ("5", "端到端测试", "完整流程验证"),
                ("a", "全部执行", "按顺序执行所有步骤"),
                ("q", "退出", "退出验证工具"),
            ]

            for num, name, desc in steps:
                # 检查步骤是否已完成
                status = ""
                if num in ["1", "2", "3", "4", "5"]:
                    step_enum = {
                        "1": ValidationStep.CONNECTION,
                        "2": ValidationStep.VECTORIZATION,
                        "3": ValidationStep.RETRIEVAL,
                        "4": ValidationStep.ANALYSIS,
                        "5": ValidationStep.END_TO_END,
                    }.get(num)

                    if step_enum in self.step_results:
                        result = self.step_results[step_enum]
                        status = " ✅" if result.success else " ❌"

                tree.add(f"[bold]{num}[/bold]. {name}{status} - [dim]{desc}[/dim]")

            self._print_rich(tree)
            choice = Prompt.ask(
                "请选择", choices=["1", "2", "3", "4", "5", "a", "q"], default="1"
            )
        else:
            print("\n选择验证步骤:")
            print("1. 连接测试 - 测试Redis、ChromaDB、Celery连接")
            print("2. 文件向量化 - 测试文件向量化到ChromaDB")
            print("3. 向量检索 - 验证ChromaDB数据检索")
            print("4. 风险分析 - 测试基于向量的风险分析")
            print("5. 端到端测试 - 完整流程验证")
            print("a. 全部执行 - 按顺序执行所有步骤")
            print("q. 退出 - 退出验证工具")

            choice = input("请选择 (1-5/a/q, 默认 1): ").strip() or "1"

        return choice

    def display_step_result(self, step: ValidationStep, result: StepResult):
        """显示步骤结果"""
        step_names = {
            ValidationStep.CONNECTION: "连接测试",
            ValidationStep.VECTORIZATION: "文件向量化",
            ValidationStep.RETRIEVAL: "向量检索",
            ValidationStep.ANALYSIS: "风险分析",
            ValidationStep.END_TO_END: "端到端测试",
        }

        step_name = step_names.get(step, str(step))
        status = "✅ 成功" if result.success else "❌ 失败"

        if self.console:
            # 创建结果面板
            title = f"{step_name} {status}"
            content = []

            content.append(f"📝 结果: {result.message}")
            content.append(f"⏱️  耗时: {result.duration:.2f}秒")

            if result.error_code:
                content.append(f"🔢 错误代码: {result.error_code}")

            # 显示关键细节
            if result.details:
                content.append("\n📋 详细信息:")

                # 特殊处理不同步骤的测试结果
                if step == ValidationStep.VECTORIZATION:
                    self._display_vectorization_details(content, result.details)
                elif step == ValidationStep.ANALYSIS:
                    self._display_analysis_details(content, result.details)
                else:
                    for key, value in result.details.items():
                        if isinstance(value, dict):
                            if value.get("success"):
                                content.append(
                                    f"  ✅ {key}: {value.get('version', 'OK')}"
                                )
                            else:
                                content.append(
                                    f"  ❌ {key}: {value.get('error', 'Failed')}"
                                )
                        else:
                            content.append(f"  • {key}: {value}")

                # 显示错误分析和建议
                if not result.success and "error_analysis" in result.details:
                    error_analysis = result.details["error_analysis"]
                    content.append(f"\n🔍 错误分析:")
                    content.append(
                        f"  错误类型: {error_analysis.get('error_type', 'Unknown')}"
                    )
                    content.append(
                        f"  错误代码: {error_analysis.get('error_code', 'UNKNOWN')}"
                    )

                    if "suggestions" in error_analysis:
                        content.append(f"\n💡 建议解决方案:")
                        for i, suggestion in enumerate(
                            error_analysis["suggestions"][:3], 1
                        ):
                            content.append(f"  {i}. {suggestion}")

                    # 显示系统指标（如果可用）
                    if "system_metrics" in error_analysis:
                        metrics = error_analysis["system_metrics"]
                        if "cpu_percent" in metrics:
                            content.append(f"\n📊 系统状态:")
                            content.append(
                                f"  CPU: {metrics.get('cpu_percent', 'N/A'):.1f}%"
                            )
                            content.append(
                                f"  内存: {metrics.get('memory_percent', 'N/A'):.1f}%"
                            )
                            if "available_memory_gb" in metrics:
                                content.append(
                                    f"  可用内存: {metrics['available_memory_gb']:.1f}GB"
                                )

            style = "green" if result.success else "red"
            self._print_rich(Panel("\n".join(content), title=title, style=style))
        else:
            print(f"\n{step_name} {status}")
            print(f"结果: {result.message}")
            print(f"耗时: {result.duration:.2f}秒")
            if result.error_code:
                print(f"错误代码: {result.error_code}")

            # 简化的结果显示
            if step == ValidationStep.VECTORIZATION and result.details:
                self._display_vectorization_details_simple(result.details)
            elif step == ValidationStep.ANALYSIS and result.details:
                self._display_analysis_details_simple(result.details)

    def _display_vectorization_details(
        self, content: List[str], details: Dict[str, Any]
    ):
        """显示向量化测试的详细结果（Rich版本）"""
        # 文件信息
        if "file_info" in details:
            file_info = details["file_info"]
            content.append(f"  📁 测试文件: {file_info['file_name']}")
            content.append(f"  📏 文件大小: {file_info['file_size']} 字节")
            content.append(f"  🏷️  文件类型: {file_info['file_type']}")
            content.append(f"  🔑 文件编码: {file_info['file_code']}")

        # 文件验证结果
        if "file_validation" in details:
            validation = details["file_validation"]
            if validation["valid"]:
                content.append(f"  ✅ 文件验证: 通过")
                if "line_count" in validation:
                    content.append(f"    行数: {validation['line_count']}")
                if "char_count" in validation:
                    content.append(f"    字符数: {validation['char_count']}")
            else:
                content.append(f"  ❌ 文件验证: {validation.get('error', '失败')}")

        # 向量化结果
        if "vectorization" in details:
            vectorization = details["vectorization"]
            if vectorization["success"]:
                content.append(
                    f"  ✅ 向量化: {vectorization.get('document_count', 0)}个文档块, {vectorization.get('total_duration', 0):.2f}s"
                )
            else:
                content.append(f"  ❌ 向量化: {vectorization.get('error', '失败')}")

        # 存储验证结果
        if "storage_verification" in details:
            storage = details["storage_verification"]
            if storage["success"]:
                content.append(
                    f"  ✅ ChromaDB存储: {storage.get('document_count', 0)}个文档"
                )
            else:
                content.append(f"  ❌ ChromaDB存储: {storage.get('error', '失败')}")

        # 检索测试结果 - 显示BGE-M3相关信息
        if "retrieval_test" in details:
            retrieval = details["retrieval_test"]
            if retrieval["success"]:
                embed_service = retrieval.get("embedding_service", "未知")
                embed_dim = retrieval.get("query_embedding_dim", "未知")
                content.append(
                    f"  ✅ 向量检索: 找到{retrieval.get('results_count', 0)}个结果"
                )
                content.append(f"    嵌入服务: {embed_service}")
                content.append(f"    查询向量维度: {embed_dim}")
                if (
                    "min_distance" in retrieval
                    and retrieval["min_distance"] is not None
                ):
                    content.append(f"    最高相似度: {1-retrieval['min_distance']:.3f}")
                    content.append(f"    距离范围: {retrieval['min_distance']:.3f} - {retrieval.get('max_distance', 0):.3f}")
            else:
                content.append(f"  ❌ 向量检索: {retrieval.get('error', '失败')}")
                # 如果是BGE嵌入服务相关错误，给出更详细的提示
                error_msg = retrieval.get('error', '')
                if 'BGE嵌入服务' in error_msg or 'embed' in error_msg.lower():
                    content.append(f"    💡 提示: 检查BGE-M3模型是否正确加载")

    def _display_analysis_details(
        self, content: List[str], details: Dict[str, Any]
    ):
        """显示风险分析测试的详细结果（Rich版本）"""
        # 文件编码查询结果
        if "file_codes_query" in details:
            file_codes_query = details["file_codes_query"]
            if file_codes_query["success"]:
                content.append(f"  ✅ 文件编码查询: 找到 {file_codes_query['total_count']} 个文件")
            else:
                content.append(f"  ❌ 文件编码查询: {file_codes_query.get('error', '失败')}")

        # 选择的文件编码
        if "selected_file_code" in details:
            selected = details["selected_file_code"]
            content.append(f"  🎯 选择文件编码: {selected['file_code']}")
            content.append(f"    文档数量: {selected.get('document_count', 0)}")
            content.append(f"    集合名称: {selected.get('collection_name', '未知')}")

        # 分析任务结果
        if "analysis_task" in details:
            analysis_task = details["analysis_task"]
            if analysis_task["success"]:
                result_data = analysis_task.get("result", {})
                content.append(
                    f"  ✅ 风险分析任务: 完成，耗时 {analysis_task.get('total_duration', 0):.2f}s"
                )

                # 显示分析结果的关键信息
                if isinstance(result_data, dict):
                    if "status" in result_data:
                        content.append(f"    状态: {result_data['status']}")
                    if "processing_time" in result_data:
                        content.append(f"    处理时间: {result_data['processing_time']:.2f}s")
                    if "document_count" in result_data:
                        content.append(f"    处理文档: {result_data['document_count']} 个")
                    if "analysis_content" in result_data:
                        content_length = len(str(result_data["analysis_content"]))
                        content.append(f"    分析内容长度: {content_length} 字符")
            else:
                content.append(f"  ❌ 风险分析任务: {analysis_task.get('error', '失败')}")

        # 结果验证
        if "result_validation" in details:
            validation = details["result_validation"]
            if validation["success"]:
                content.append(f"  ✅ 结果验证: 通过")

                # 显示验证检查项
                if "checks" in validation:
                    checks = validation["checks"]
                    for check_name, check_result in checks.items():
                        content.append(f"    {check_name}: {check_result}")

                # 显示警告
                if "warnings" in validation and validation["warnings"]:
                    content.append(f"    ⚠️ 警告: {len(validation['warnings'])} 项")
                    for warning in validation["warnings"][:3]:  # 最多显示3个警告
                        content.append(f"      • {warning}")
            else:
                content.append(f"  ❌ 结果验证: {validation.get('error', '失败')}")

                # 显示验证错误
                if "errors" in validation and validation["errors"]:
                    content.append(f"    错误: {len(validation['errors'])} 项")
                    for error in validation["errors"][:3]:  # 最多显示3个错误
                        content.append(f"      • {error}")

    def _display_analysis_details_simple(self, details: Dict[str, Any]):
        """显示风险分析测试的详细结果（简化版本）"""
        # 文件编码查询结果
        if "file_codes_query" in details:
            file_codes_query = details["file_codes_query"]
            if file_codes_query["success"]:
                print(f"  📋 文件编码查询: 找到 {file_codes_query['total_count']} 个文件")
            else:
                print(f"  ❌ 文件编码查询: {file_codes_query.get('error', '失败')}")

        # 选择的文件编码
        if "selected_file_code" in details:
            selected = details["selected_file_code"]
            print(f"  🎯 选择文件编码: {selected['file_code']}")
            print(f"    文档数量: {selected.get('document_count', 0)}")

        # 分析任务结果
        if "analysis_task" in details:
            analysis_task = details["analysis_task"]
            if analysis_task["success"]:
                result_data = analysis_task.get("result", {})
                print(f"  ✅ 风险分析任务: 完成，耗时 {analysis_task.get('total_duration', 0):.2f}s")

                if isinstance(result_data, dict):
                    if "status" in result_data:
                        print(f"    状态: {result_data['status']}")
                    if "document_count" in result_data:
                        print(f"    处理文档: {result_data['document_count']} 个")
            else:
                print(f"  ❌ 风险分析任务: {analysis_task.get('error', '失败')}")

        # 结果验证
        if "result_validation" in details:
            validation = details["result_validation"]
            if validation["success"]:
                print(f"  ✅ 结果验证: 通过")
                if "warnings" in validation and validation["warnings"]:
                    print(f"    ⚠️ 警告: {len(validation['warnings'])} 项")
            else:
                print(f"  ❌ 结果验证: {validation.get('error', '失败')}")

    def _display_vectorization_details_simple(self, details: Dict[str, Any]):
        """显示向量化测试的详细结果（简化版本）"""
        # 文件信息
        if "file_info" in details:
            file_info = details["file_info"]
            print(f"  📁 测试文件: {file_info['file_name']}")
            print(f"  📏 文件大小: {file_info['file_size']} 字节")

        # 向量化结果
        if "vectorization" in details:
            vectorization = details["vectorization"]
            status = "✅" if vectorization["success"] else "❌"
            print(
                f"  {status} 向量化: {vectorization.get('document_count', 0)}个文档块"
            )

        # 存储验证结果
        if "storage_verification" in details:
            storage = details["storage_verification"]
            status = "✅" if storage["success"] else "❌"
            print(f"  {status} ChromaDB存储: {storage.get('document_count', 0)}个文档")

        # 检索测试结果 - 简化版本也显示嵌入服务信息
        if "retrieval_test" in details:
            retrieval = details["retrieval_test"]
            status = "✅" if retrieval["success"] else "❌"
            results_count = retrieval.get('results_count', 0)
            embed_service = retrieval.get("embedding_service", "未知")
            print(f"  {status} 向量检索: {results_count}个结果 (使用{embed_service})")
            if retrieval["success"] and "min_distance" in retrieval and retrieval["min_distance"] is not None:
                print(f"    最高相似度: {1-retrieval['min_distance']:.3f}")

    async def cleanup(self):
        """清理资源"""
        cleanup_start = time.time()
        cleanup_results = {
            "files_cleaned": 0,
            "files_failed": 0,
            "connections_closed": 0,
            "embedding_service_cleaned": False,
            "errors": [],
        }

        try:
            # 清理BGE嵌入服务
            if self._embedding_service:
                try:
                    self._log("清理BGE嵌入服务...")
                    if hasattr(self._embedding_service, 'cleanup_memory'):
                        self._embedding_service.cleanup_memory()
                    cleanup_results["embedding_service_cleaned"] = True
                    self._embedding_service = None
                    self._log("✅ BGE嵌入服务清理完成")
                except Exception as e:
                    cleanup_results["errors"].append(f"BGE嵌入服务清理失败: {e}")
                    self._log(f"BGE嵌入服务清理失败: {e}", "warning")

            # 清理测试文件
            test_files = self.test_data.get("test_files", [])
            if test_files:
                self._log(f"开始清理 {len(test_files)} 个测试文件...")

                for file_path in test_files:
                    try:
                        if isinstance(file_path, Path) and file_path.exists():
                            await asyncio.to_thread(file_path.unlink)
                            cleanup_results["files_cleaned"] += 1
                        elif isinstance(file_path, str) and Path(file_path).exists():
                            await asyncio.to_thread(Path(file_path).unlink)
                            cleanup_results["files_cleaned"] += 1
                    except Exception as e:
                        cleanup_results["files_failed"] += 1
                        cleanup_results["errors"].append(
                            f"文件清理失败 {file_path}: {e}"
                        )
                        self._log(f"清理文件失败 {file_path}: {e}", "warning")

            # 关闭客户端连接
            connection_tasks = []

            if self._redis_client:
                try:
                    connection_tasks.append(asyncio.to_thread(self._redis_client.close))
                except Exception as e:
                    cleanup_results["errors"].append(f"Redis连接关闭失败: {e}")

            if self._chroma_client:
                try:
                    connection_tasks.append(self._chroma_client.aclose())
                except Exception as e:
                    cleanup_results["errors"].append(f"ChromaDB连接关闭失败: {e}")

            if connection_tasks:
                try:
                    await asyncio.gather(*connection_tasks, return_exceptions=True)
                    cleanup_results["connections_closed"] = len(connection_tasks)
                except Exception as e:
                    cleanup_results["errors"].append(f"连接关闭过程异常: {e}")

            # 清理内存中的测试数据
            if self.test_data:
                data_size = len(str(self.test_data))
                self.test_data.clear()
                self._log(f"清理测试数据 (~{data_size}字符)")

            cleanup_time = time.time() - cleanup_start

            # 记录清理结果
            if cleanup_results["errors"]:
                self._log(
                    f"资源清理完成，耗时 {cleanup_time:.2f}s，部分失败: "
                    f"文件 {cleanup_results['files_cleaned']}/{cleanup_results['files_cleaned'] + cleanup_results['files_failed']}, "
                    f"连接 {cleanup_results['connections_closed']}, "
                    f"嵌入服务 {'✅' if cleanup_results['embedding_service_cleaned'] else '❌'}",
                    "warning",
                )
            else:
                self._log(
                    f"资源清理完成，耗时 {cleanup_time:.2f}s: "
                    f"文件 {cleanup_results['files_cleaned']}, "
                    f"连接 {cleanup_results['connections_closed']}, "
                    f"嵌入服务 {'✅' if cleanup_results['embedding_service_cleaned'] else '❌'}"
                )

        except Exception as e:
            self._log(f"清理过程中出现严重错误: {e}", "error")
            cleanup_results["errors"].append(f"清理异常: {e}")

        return cleanup_results


def main():
    """主函数"""
    try:
        # 环境检查
        if not Path(project_root / "services" / "worker_service").exists():
            print("❌ 请在项目根目录运行此脚本")
            sys.exit(1)

        # 创建验证器
        config = StepConfig()
        validator = WorkerStepValidator(config)

        # 显示头部信息
        validator.print_header()

        # 主循环
        async def run_validation():
            try:
                while True:
                    choice = validator.show_step_menu()

                    if choice == "q":
                        validator._log("👋 用户退出验证工具")
                        break
                    elif choice == "1":
                        result = await validator.step_1_connection_test()
                        validator.step_results[ValidationStep.CONNECTION] = result
                        validator.display_step_result(ValidationStep.CONNECTION, result)
                    elif choice == "2":
                        result = await validator.step_2_vectorization_test()
                        validator.step_results[ValidationStep.VECTORIZATION] = result
                        validator.display_step_result(
                            ValidationStep.VECTORIZATION, result
                        )
                    elif choice == "3":
                        validator._log(f"ℹ️ 步骤 3 将在后续版本中实现")
                    elif choice == "4":
                        result = await validator.step_4_analysis_test()
                        validator.step_results[ValidationStep.ANALYSIS] = result
                        validator.display_step_result(ValidationStep.ANALYSIS, result)
                    elif choice == "5":
                        validator._log(f"ℹ️ 步骤 5 将在后续版本中实现")
                    elif choice == "a":
                        validator._log("🚀 开始执行全部验证步骤...")

                        # 按顺序执行所有步骤
                        steps_to_run = [
                            (
                                ValidationStep.CONNECTION,
                                validator.step_1_connection_test,
                                "连接测试",
                            ),
                            (
                                ValidationStep.VECTORIZATION,
                                validator.step_2_vectorization_test,
                                "文件向量化测试",
                            ),
                            (
                                ValidationStep.ANALYSIS,
                                validator.step_4_analysis_test,
                                "风险分析测试",
                            ),
                        ]

                        all_success = True
                        for step_enum, step_method, step_name in steps_to_run:
                            validator._log(f"🔄 执行 {step_name}...")
                            result = await step_method()
                            validator.step_results[step_enum] = result
                            validator.display_step_result(step_enum, result)

                            if not result.success:
                                all_success = False
                                should_continue = True
                                if validator.console:
                                    should_continue = Confirm.ask(
                                        f"❌ {step_name}失败，是否继续执行其他步骤？"
                                    )
                                else:
                                    user_input = (
                                        input(
                                            f"❌ {step_name}失败，是否继续执行其他步骤？(y/n): "
                                        )
                                        .strip()
                                        .lower()
                                    )
                                    should_continue = user_input in ["y", "yes", "是"]

                                if not should_continue:
                                    validator._log("⏹️ 用户选择停止执行后续步骤")
                                    break

                        # 显示总结
                        total_time = sum(
                            r.duration for r in validator.step_results.values()
                        )
                        success_count = sum(
                            1 for r in validator.step_results.values() if r.success
                        )
                        total_count = len(validator.step_results)

                        if validator.console:
                            summary_table = Table(
                                title="🏁 全部步骤执行完成", box=box.ROUNDED
                            )
                            summary_table.add_column("步骤", style="cyan")
                            summary_table.add_column("状态", style="white")
                            summary_table.add_column("耗时", style="yellow")
                            summary_table.add_column("消息", style="white")

                            step_names = {
                                ValidationStep.CONNECTION: "连接测试",
                                ValidationStep.VECTORIZATION: "文件向量化",
                                ValidationStep.RETRIEVAL: "向量检索",
                                ValidationStep.ANALYSIS: "风险分析",
                                ValidationStep.END_TO_END: "端到端测试",
                            }

                            for step, result in validator.step_results.items():
                                status = "✅ 成功" if result.success else "❌ 失败"
                                name = step_names.get(step, str(step))
                                summary_table.add_row(
                                    name,
                                    status,
                                    f"{result.duration:.2f}s",
                                    (
                                        result.message[:50] + "..."
                                        if len(result.message) > 50
                                        else result.message
                                    ),
                                )

                            validator._print_rich(summary_table)
                            validator._print_rich(
                                f"\n📊 总计: {success_count}/{total_count} 步骤成功，总耗时 {total_time:.2f}s"
                            )
                        else:
                            validator._log(
                                f"🏁 全部步骤执行完成: {success_count}/{total_count} 步骤成功，总耗时 {total_time:.2f}s"
                            )

                        # ℹ️ 其他步骤将在后续版本中实现
                        validator._log("ℹ️ 步骤3-5将在后续版本中实现")
                    else:
                        validator._log(f"ℹ️ 步骤 {choice} 将在后续版本中实现")

                    if validator.console:
                        input("\n按回车键继续...")
                    else:
                        print()

            finally:
                await validator.cleanup()

        # 运行验证
        asyncio.run(run_validation())

    except KeyboardInterrupt:
        print("\n👋 用户中断，退出验证工具")
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        if config.verbose:
            traceback.print_exc()


if __name__ == "__main__":
    main()
