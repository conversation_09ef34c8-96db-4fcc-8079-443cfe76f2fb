#!/usr/bin/env python3
"""
Deep Risk RAG Worker 管理脚本
用于启动、停止和监控Worker服务
"""

import os
import sys
import time
import signal
import subprocess
import psutil
from pathlib import Path
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Worker管理脚本已创建！")

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    from rich import box
    HAS_RICH = True
except ImportError:
    HAS_RICH = False

try:
    from shared.utils.logger import get_logger
    from shared.celery_config import celery_app
    from shared.redis_config import redis_config
    from services.worker_service.config import config as worker_config
    HAS_PROJECT_MODULES = True
except ImportError as e:
    HAS_PROJECT_MODULES = False
    print(f"⚠️ 项目模块导入失败: {e}")


class WorkerManager:
    """Worker服务管理器"""
    
    def __init__(self):
        self.console = Console() if HAS_RICH else None
        self.logger = get_logger(__name__) if HAS_PROJECT_MODULES else None
        self.worker_processes: List[psutil.Process] = []
    
    def _log(self, message: str, level: str = "info"):
        """日志输出"""
        if self.logger:
            getattr(self.logger, level)(message)
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def _print_rich(self, content, **kwargs):
        """Rich输出包装"""
        if self.console:
            self.console.print(content, **kwargs)
        else:
            print(str(content))
    
    def find_worker_processes(self) -> List[Dict]:
        """查找所有Worker进程"""
        workers = []
        
        try:
            for proc in psutil.process_iter(['pid', 'cmdline', 'create_time', 'memory_info']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) > 2:
                        cmdline_str = ' '.join(cmdline)
                        if 'celery' in cmdline_str and 'worker' in cmdline_str and 'services.worker_service' in cmdline_str:
                            workers.append({
                                'pid': proc.info['pid'],
                                'cmdline': cmdline_str,
                                'create_time': proc.info['create_time'],
                                'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0,
                                'process': proc
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self._log(f"查找Worker进程失败: {e}", "error")
        
        return workers
    
    def stop_all_workers(self) -> bool:
        """停止所有Worker进程"""
        workers = self.find_worker_processes()
        
        if not workers:
            self._log("没有找到运行中的Worker进程")
            return True
        
        self._log(f"找到 {len(workers)} 个Worker进程，开始停止...")
        
        # 尝试优雅停止
        for worker in workers:
            try:
                proc = worker['process']
                self._log(f"停止Worker进程 PID {worker['pid']}...")
                proc.terminate()
            except Exception as e:
                self._log(f"停止进程 {worker['pid']} 失败: {e}", "warning")
        
        # 等待进程结束
        time.sleep(3)
        
        # 检查是否还有进程存活
        remaining = self.find_worker_processes()
        if remaining:
            self._log(f"强制结束 {len(remaining)} 个残余进程...")
            for worker in remaining:
                try:
                    worker['process'].kill()
                except Exception as e:
                    self._log(f"强制结束进程 {worker['pid']} 失败: {e}", "warning")
        
        # 最终检查
        time.sleep(1)
        final_check = self.find_worker_processes()
        
        if final_check:
            self._log(f"❌ 仍有 {len(final_check)} 个Worker进程未能停止", "error")
            return False
        else:
            self._log("✅ 所有Worker进程已停止")
            return True
    
    def start_worker(self, background: bool = True) -> bool:
        """启动Worker服务"""
        try:
            # 检查是否已有Worker运行
            existing_workers = self.find_worker_processes()
            if existing_workers:
                self._log(f"发现 {len(existing_workers)} 个已运行的Worker进程")
                if self.console:
                    should_stop = Confirm.ask("是否停止现有Worker并重新启动？")
                else:
                    should_stop = input("是否停止现有Worker并重新启动？(y/n): ").lower() == 'y'
                
                if should_stop:
                    if not self.stop_all_workers():
                        return False
                else:
                    self._log("取消启动，保持现有Worker运行")
                    return True
            
            # 构建启动命令
            cmd = [
                "celery", "-A", "services.worker_service.worker", "worker",
                "--pool", "processes",
                "--concurrency", "1",
                "--max-tasks-per-child", "10",
                "--loglevel", "info",
                "--prefetch-multiplier", "1"
            ]
            
            self._log(f"启动Worker命令: {' '.join(cmd)}")
            
            if background:
                # 后台启动
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    cwd=str(project_root)
                )
                
                # 等待一下确保启动
                time.sleep(3)
                
                # 检查进程是否还在运行
                if process.poll() is None:
                    self._log(f"✅ Worker已在后台启动 (PID: {process.pid})")
                    return True
                else:
                    self._log(f"❌ Worker启动失败，进程已退出", "error")
                    return False
            else:
                # 前台启动
                self._log("🚀 启动Worker（前台模式）...")
                self._log("按 Ctrl+C 停止Worker")
                subprocess.run(cmd, cwd=str(project_root))
                return True
                
        except FileNotFoundError:
            self._log("❌ celery命令未找到，请确保已安装celery", "error")
            return False
        except Exception as e:
            self._log(f"❌ 启动Worker失败: {e}", "error")
            return False
    
    def check_worker_status(self) -> Dict:
        """检查Worker状态"""
        status = {
            'processes': [],
            'total_processes': 0,
            'total_memory_mb': 0,
            'celery_reachable': False,
            'redis_connected': False
        }
        
        # 检查进程
        workers = self.find_worker_processes()
        status['processes'] = workers
        status['total_processes'] = len(workers)
        status['total_memory_mb'] = sum(w['memory_mb'] for w in workers)
        
        # 检查Celery连接
        if HAS_PROJECT_MODULES:
            try:
                inspect = celery_app.control.inspect()
                stats = inspect.stats()
                status['celery_reachable'] = bool(stats)
            except Exception:
                status['celery_reachable'] = False
            
            # 检查Redis连接
            try:
                redis_config.client.ping()
                status['redis_connected'] = True
            except Exception:
                status['redis_connected'] = False
        
        return status
    
    def show_worker_status(self):
        """显示Worker状态"""
        status = self.check_worker_status()
        
        if self.console:
            # 创建状态面板
            status_info = []
            
            # 进程信息
            if status['total_processes'] > 0:
                status_info.append(f"🟢 Worker进程: {status['total_processes']} 个")
                status_info.append(f"💾 内存使用: {status['total_memory_mb']:.1f} MB")
            else:
                status_info.append("🔴 Worker进程: 未运行")
            
            # 服务连接状态
            redis_status = "🟢 已连接" if status['redis_connected'] else "🔴 未连接"
            celery_status = "🟢 可达" if status['celery_reachable'] else "🔴 不可达"
            
            status_info.append(f"Redis: {redis_status}")
            status_info.append(f"Celery: {celery_status}")
            
            self._print_rich(Panel("\n".join(status_info), title="🔧 Worker服务状态", style="cyan"))
            
            # 进程详情表格
            if status['processes']:
                table = Table(title="Worker进程详情", box=box.ROUNDED)
                table.add_column("PID", style="cyan")
                table.add_column("内存(MB)", style="yellow")
                table.add_column("运行时间", style="green")
                table.add_column("命令行", style="dim")
                
                for worker in status['processes']:
                    runtime = time.time() - worker['create_time']
                    runtime_str = f"{runtime/60:.1f}分钟" if runtime > 60 else f"{runtime:.1f}秒"
                    
                    cmdline_short = worker['cmdline']
                    if len(cmdline_short) > 60:
                        cmdline_short = cmdline_short[:57] + "..."
                    
                    table.add_row(
                        str(worker['pid']),
                        f"{worker['memory_mb']:.1f}",
                        runtime_str,
                        cmdline_short
                    )
                
                self._print_rich(table)
        else:
            # 简化显示
            print(f"\n🔧 Worker服务状态:")
            print(f"  进程数: {status['total_processes']}")
            print(f"  内存使用: {status['total_memory_mb']:.1f} MB")
            print(f"  Redis连接: {'✅' if status['redis_connected'] else '❌'}")
            print(f"  Celery连接: {'✅' if status['celery_reachable'] else '❌'}")
    
    def show_main_menu(self):
        """显示主菜单"""
        while True:
            status = self.check_worker_status()
            
            if self.console:
                self._print_rich("\n[bold cyan]🔧 Worker服务管理[/bold cyan]")
                
                # 状态摘要
                if status['total_processes'] > 0:
                    self._print_rich(f"[green]当前状态: {status['total_processes']} 个Worker进程运行中[/green]")
                else:
                    self._print_rich("[red]当前状态: 无Worker进程运行[/red]")
                
                options = [
                    "1. 查看Worker状态",
                    "2. 启动Worker（后台）",
                    "3. 启动Worker（前台）", 
                    "4. 停止所有Worker",
                    "5. 重启Worker",
                    "q. 退出管理工具"
                ]
                
                for option in options:
                    self._print_rich(option)
                
                choice = Prompt.ask("请选择操作", choices=["1", "2", "3", "4", "5", "q"], default="1")
            else:
                print(f"\n🔧 Worker服务管理")
                print(f"当前状态: {status['total_processes']} 个Worker进程")
                print("1. 查看Worker状态")
                print("2. 启动Worker（后台）")
                print("3. 启动Worker（前台）")
                print("4. 停止所有Worker")
                print("5. 重启Worker")
                print("q. 退出管理工具")
                
                choice = input("请选择操作 (1-5/q): ").strip() or "1"
            
            if choice == "1":
                self.show_worker_status()
            elif choice == "2":
                self.start_worker(background=True)
            elif choice == "3":
                self.start_worker(background=False)
                break  # 前台模式会阻塞，运行完毕后退出
            elif choice == "4":
                self.stop_all_workers()
            elif choice == "5":
                self._log("重启Worker服务...")
                if self.stop_all_workers():
                    time.sleep(2)
                    self.start_worker(background=True)
                else:
                    self._log("停止Worker失败，取消重启", "error")
            elif choice == "q":
                self._log("退出Worker管理工具")
                break
            else:
                self._log("无效选择")
            
            if choice != "q":
                if self.console:
                    input("\n按回车键继续...")
                else:
                    input("按回车键继续...")


def main():
    """主函数"""
    try:
        if not HAS_PROJECT_MODULES:
            print("❌ 项目模块未正确导入，请检查环境配置")
            sys.exit(1)
        
        manager = WorkerManager()
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            action = sys.argv[1].lower()
            
            if action == "start":
                background = "--foreground" not in sys.argv
                manager.start_worker(background=background)
            elif action == "stop":
                manager.stop_all_workers()
            elif action == "restart":
                if manager.stop_all_workers():
                    time.sleep(2)
                    manager.start_worker(background=True)
            elif action == "status":
                manager.show_worker_status()
            else:
                print("用法: python worker_manager.py [start|stop|restart|status]")
                print("  start         - 启动Worker（后台）")
                print("  start --foreground - 启动Worker（前台）")
                print("  stop          - 停止所有Worker")
                print("  restart       - 重启Worker")
                print("  status        - 查看Worker状态")
                print("  (无参数)       - 进入交互式管理界面")
        else:
            # 交互式界面
            manager.show_main_menu()
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出管理工具")
    except Exception as e:
        print(f"❌ 管理工具运行出错: {e}")


if __name__ == "__main__":
    main() 