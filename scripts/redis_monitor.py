#!/usr/bin/env python3
"""
Deep Risk RAG Redis监控和诊断工具
用于监控Redis使用情况、检查键空间、诊断问题
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.redis_config import redis_config, get_redis_client
from shared.utils.logger import setup_logging, get_logger

setup_logging(level="INFO", service_name="redis-monitor")
logger = get_logger(__name__)


class RedisMonitor:
    """Redis监控器"""
    
    def __init__(self):
        self.client = get_redis_client()
        self.prefix = redis_config.key_prefix
        
    def check_connection(self) -> bool:
        """检查Redis连接"""
        try:
            self.client.ping()
            logger.info("✅ Redis连接正常")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    def get_server_info(self) -> Dict:
        """获取Redis服务器信息"""
        try:
            info = self.client.info()
            return {
                'version': info.get('redis_version'),
                'mode': info.get('redis_mode'),
                'memory_used': info.get('used_memory_human'),
                'memory_peak': info.get('used_memory_peak_human'),
                'connected_clients': info.get('connected_clients'),
                'total_commands_processed': info.get('total_commands_processed'),
                'db_count': len([k for k in info.keys() if k.startswith('db')])
            }
        except Exception as e:
            logger.error(f"获取服务器信息失败: {e}")
            return {}
    
    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        try:
            info = self.client.info('keyspace')
            db_info = {}
            
            for key, value in info.items():
                if key.startswith('db'):
                    db_num = key[2:]
                    # 解析值，如: 'keys=2,expires=0,avg_ttl=0'
                    stats = {}
                    for stat in value.split(','):
                        k, v = stat.split('=')
                        stats[k] = int(v)
                    db_info[db_num] = stats
                    
            return db_info
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    def get_project_keys(self) -> List[str]:
        """获取项目相关的所有键"""
        try:
            pattern = f"{self.prefix}:*"
            keys = self.client.keys(pattern)
            return sorted(keys)
        except Exception as e:
            logger.error(f"获取项目键失败: {e}")
            return []
    
    def analyze_key_patterns(self, keys: List[str]) -> Dict:
        """分析键模式"""
        patterns = {}
        
        for key in keys:
            # 移除项目前缀
            clean_key = key.replace(f"{self.prefix}:", "", 1)
            
            # 提取模式
            parts = clean_key.split(":")
            if len(parts) > 0:
                pattern = parts[0]
                if pattern not in patterns:
                    patterns[pattern] = []
                patterns[pattern].append(key)
        
        return patterns
    
    def get_key_details(self, key: str) -> Dict:
        """获取键详细信息"""
        try:
            key_type = self.client.type(key)
            ttl = self.client.ttl(key)
            size = self.client.memory_usage(key) if hasattr(self.client, 'memory_usage') else 0
            
            details = {
                'type': key_type,
                'ttl': ttl,
                'size_bytes': size
            }
            
            # 根据类型获取更多信息
            if key_type == 'string':
                details['length'] = self.client.strlen(key)
            elif key_type == 'list':
                details['length'] = self.client.llen(key)
            elif key_type == 'set':
                details['length'] = self.client.scard(key)
            elif key_type == 'hash':
                details['length'] = self.client.hlen(key)
            elif key_type == 'zset':
                details['length'] = self.client.zcard(key)
                
            return details
        except Exception as e:
            logger.error(f"获取键详情失败 {key}: {e}")
            return {}
    
    def check_celery_queues(self) -> Dict:
        """检查Celery队列状态"""
        queue_status = {}
        queue_names = ['default', 'vectorization', 'analysis', 'health']
        
        for queue in queue_names:
            queue_key = f"{self.prefix}:{queue}"
            try:
                length = self.client.llen(queue_key)
                queue_status[queue] = {
                    'length': length,
                    'exists': self.client.exists(queue_key)
                }
            except Exception as e:
                queue_status[queue] = {'error': str(e)}
        
        return queue_status
    
    def cleanup_expired_keys(self, dry_run: bool = True) -> int:
        """清理过期键"""
        project_keys = self.get_project_keys()
        expired_count = 0
        
        for key in project_keys:
            ttl = self.client.ttl(key)
            if ttl == -1:  # 没有过期时间的键
                continue
            elif ttl == -2:  # 已过期但未删除的键
                if not dry_run:
                    self.client.delete(key)
                expired_count += 1
                logger.info(f"{'[DRY RUN] ' if dry_run else ''}删除过期键: {key}")
        
        return expired_count
    
    def generate_report(self) -> Dict:
        """生成监控报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'connection': self.check_connection(),
            'server_info': self.get_server_info(),
            'database_info': self.get_database_info(),
            'project_config': {
                'redis_url': redis_config.url,
                'database': redis_config.db,
                'key_prefix': redis_config.key_prefix
            }
        }
        
        if report['connection']:
            project_keys = self.get_project_keys()
            key_patterns = self.analyze_key_patterns(project_keys)
            
            report.update({
                'project_keys_count': len(project_keys),
                'key_patterns': {k: len(v) for k, v in key_patterns.items()},
                'celery_queues': self.check_celery_queues(),
                'sample_keys': project_keys[:10]  # 显示前10个键作为样本
            })
        
        return report


def print_report(report: Dict):
    """打印格式化的报告"""
    print("=" * 60)
    print(f"🔴 Deep Risk RAG Redis监控报告")
    print(f"📅 时间: {report['timestamp']}")
    print("=" * 60)
    
    # 连接状态
    status = "✅ 正常" if report['connection'] else "❌ 失败"
    print(f"🔗 连接状态: {status}")
    
    # 项目配置
    config = report.get('project_config', {})
    print(f"🔧 Redis URL: {config.get('redis_url', 'N/A')}")
    print(f"🗄️  数据库: {config.get('database', 'N/A')}")
    print(f"🏷️  键前缀: {config.get('key_prefix', 'N/A')}")
    
    # 服务器信息
    server = report.get('server_info', {})
    if server:
        print(f"📊 Redis版本: {server.get('version', 'N/A')}")
        print(f"💾 内存使用: {server.get('memory_used', 'N/A')}")
        print(f"👥 连接数: {server.get('connected_clients', 'N/A')}")
    
    # 数据库信息
    db_info = report.get('database_info', {})
    if db_info:
        print(f"🗂️  数据库信息:")
        for db, stats in db_info.items():
            print(f"   DB{db}: {stats.get('keys', 0)} 键")
    
    # 项目键统计
    if report['connection']:
        print(f"🔑 项目键总数: {report.get('project_keys_count', 0)}")
        
        patterns = report.get('key_patterns', {})
        if patterns:
            print(f"📋 键模式分布:")
            for pattern, count in patterns.items():
                print(f"   {pattern}: {count} 个")
        
        # Celery队列状态
        queues = report.get('celery_queues', {})
        if queues:
            print(f"📦 Celery队列状态:")
            for queue, status in queues.items():
                if 'error' in status:
                    print(f"   {queue}: ❌ {status['error']}")
                else:
                    length = status.get('length', 0)
                    exists = status.get('exists', False)
                    print(f"   {queue}: {'✅' if exists else '❌'} {length} 任务")
    
    print("=" * 60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deep Risk RAG Redis监控工具")
    parser.add_argument("--json", action="store_true", help="以JSON格式输出")
    parser.add_argument("--keys", action="store_true", help="显示所有项目键")
    parser.add_argument("--cleanup", action="store_true", help="清理过期键")
    parser.add_argument("--dry-run", action="store_true", help="干跑模式（仅显示要删除的键）")
    
    args = parser.parse_args()
    
    monitor = RedisMonitor()
    
    if args.cleanup:
        count = monitor.cleanup_expired_keys(dry_run=args.dry_run)
        print(f"{'[DRY RUN] ' if args.dry_run else ''}清理了 {count} 个过期键")
        return
    
    if args.keys:
        keys = monitor.get_project_keys()
        print(f"项目相关键 ({len(keys)} 个):")
        for key in keys:
            details = monitor.get_key_details(key)
            print(f"  {key} ({details.get('type', 'unknown')})")
        return
    
    # 生成并显示报告
    report = monitor.generate_report()
    
    if args.json:
        print(json.dumps(report, indent=2, ensure_ascii=False))
    else:
        print_report(report)


if __name__ == "__main__":
    main() 