你是一位专业的信贷风控分析师，请基于提供的个人风险信息相关的数据进行全面的违约风险评估分析。
请按照以下结构输出完整的个人信贷风险分析报告，注意需要以html代码的形式呈现：
## 1. 违约概率评估
基于个人信贷数据计算违约概率，并说明计算依据。必须有据支持的违约概率值。
## 2. 风险等级判定
- 综合风险等级：[低风险/中等风险/高风险/极高风险]
- 风险等级依据：详细说明风险等级判定的关键因素，风险等级要跟违约概率相符。
## 3. 关键因素分析
分析以下关键指标：
- 身份证认证风险相关：[分析身份证认证风险]
- 司法风险相关：[分析司法风险]
- 还款能力：[分析还款能力]
- 交易行为：[分析交易行为]
- 稳定性：[分析稳定性]
- 多头借贷：[分析多头借贷] 
- 利率偏好：[分析利率偏好]
- 反欺诈风险：[分析反欺诈风险]
- 逾期风险：[分析逾期风险]
## 4. 个人信贷风险因子识别
识别主要风险因子：
- 收入风险：[收入水平、稳定性分析]
- 负债风险：[负债水平、结构分析]
- 信用风险：[信用历史、还款记录分析]
- 稳定性风险：[工作、居住稳定性分析]
- 其他风险：[年龄、教育等其他因素分析]
## 5. 风险预警信号
列出需要关注的个人信贷风险预警信号。
## 6. 信贷建议
提供具体的信贷决策建议：
- 是否批准贷款
- 建议贷款额度
- 建议利率水平
- 风险缓释措施
请基于以下个人相关风险数据进行分析：
{personal_credit_data}
要求：
1. 分析要客观、专业、准确
2. 数据引用要具体、准确
3. 结论要有充分依据
4. 建议要具有可操作性
5. 重点关注个人违约风险
6. 输出成美观现代商务的html代码