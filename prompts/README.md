# 指令模板管理

本目录包含系统使用的各种AI指令模板，支持通过编辑Markdown文件来自定义指令内容。

## 目录结构

```
prompts/
├── README.md                    # 本说明文件
└── risk_analysis/              # 风控分析相关指令
    └── default.md              # 默认风控分析指令模板
```

## 使用方法

### 1. 编辑指令模板

直接编辑对应的 `.md` 文件即可修改指令内容：

```bash
# 编辑默认风控分析指令
vim prompts/risk_analysis/default.md
```

### 2. 配置自定义指令文件

在 `config.py` 中或通过环境变量指定使用的指令文件：

```python
# config.py
RISK_ANALYSIS_PROMPT_FILE = "prompts/risk_analysis/default.md"
```

或设置环境变量：

```bash
export RISK_ANALYSIS_PROMPT_FILE="prompts/risk_analysis/custom.md"
```

### 3. 占位符使用

指令模板支持占位符，系统会在运行时自动替换：

- `{personal_credit_data}` - 个人信贷数据内容

示例：
```markdown
请分析以下数据：{personal_credit_data}
```

## 指令模板编写指南

### 1. 文件格式
- 使用 Markdown 格式 (`.md`)
- 支持标准 Markdown 语法
- 文件编码使用 UTF-8

### 2. 占位符规范
- 使用 `{变量名}` 格式
- 变量名使用小写字母和下划线
- 确保占位符在代码中有对应的替换逻辑

### 3. 内容结构建议
- 明确角色定位和任务目标
- 详细的输出格式要求
- 具体的分析维度和标准
- 清晰的约束条件

### 4. 版本管理
- 建议对重要修改进行版本控制
- 可以创建不同版本的模板文件
- 通过配置切换使用的版本

## 扩展指令模板

### 1. 创建新的业务场景指令

```bash
# 创建新的业务场景目录
mkdir prompts/new_scenario

# 创建指令模板
touch prompts/new_scenario/template.md
```

### 2. 在代码中使用新指令

参考 `src/risk_config.py` 中的实现方式，添加对应的配置和加载逻辑。

## 注意事项

1. **向后兼容性**：系统保留硬编码的备用指令，确保文件不存在时仍能正常工作
2. **文件权限**：确保指令文件具有读取权限
3. **编码格式**：使用 UTF-8 编码保存文件
4. **测试验证**：修改指令后建议进行测试验证
5. **备份重要指令**：对关键指令模板进行备份

## 故障排除

### 指令文件读取失败
- 检查文件路径是否正确
- 确认文件权限是否可读
- 验证文件编码是否为 UTF-8

### 占位符替换异常
- 检查占位符格式是否正确
- 确认代码中是否有对应的替换逻辑
- 验证传入的参数是否匹配
