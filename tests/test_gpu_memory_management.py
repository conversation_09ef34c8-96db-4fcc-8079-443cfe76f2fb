#!/usr/bin/env python3
"""
GPU显存管理功能测试脚本
"""

import os
import sys
import time
import torch
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"

from services.vector_service.core.embeddings import BGEEmbeddingService
from shared.utils.logger import setup_logging, get_logger

# 设置日志
setup_logging(level="INFO", service_name="GPU内存测试")
logger = get_logger(__name__)


def print_separator(title: str):
    """打印分隔符"""
    print("=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)


def test_gpu_memory_functionality():
    """测试GPU显存功能"""
    
    print_separator("GPU显存管理功能测试")
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    if cuda_available:
        print(f"GPU设备数量: {torch.cuda.device_count()}")
        print(f"当前GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("运行在CPU模式下")
    
    print("\n")
    
    try:
        print_separator("1. 初始化嵌入服务")
        
        # 初始化嵌入服务，启用显存监控
        embedding_service = BGEEmbeddingService(
            auto_cleanup=True,
            memory_monitor=True,
            batch_size=8  # 使用较小的批次大小进行测试
        )
        
        print("✅ 嵌入服务初始化成功")
        
        print_separator("2. 测试显存信息获取")
        
        # 测试显存信息获取
        memory_info = embedding_service.get_memory_info()
        print(f"内存信息: {memory_info}")
        
        # 检查是否为CUDA模式
        if memory_info.get("cuda_available") and memory_info.get("device") == "cuda":
            print("✅ 运行在GPU模式，将测试显存管理")
        else:
            print("ℹ️  运行在CPU模式，将测试内存管理")
        
        print_separator("3. 测试嵌入生成")
        
        # 生成一些测试文本
        test_texts = [
            "这是一个测试文档，用于验证GPU显存管理功能。",
            "风险分析是金融机构的重要业务流程。",
            "机器学习模型需要大量的计算资源。",
            "向量化服务在处理大量文档时需要优化显存使用。",
            "深度学习模型的显存管理对于系统稳定运行至关重要。"
        ] * 10  # 重复10次，增加计算负载
        
        print(f"正在处理 {len(test_texts)} 个文本...")
        
        # 记录开始时的显存状态
        embedding_service.log_memory_usage("嵌入开始前")
        
        # 执行嵌入
        start_time = time.time()
        embeddings = embedding_service.embed_documents(test_texts)
        end_time = time.time()
        
        print(f"✅ 嵌入完成，用时: {end_time - start_time:.2f}秒")
        print(f"生成嵌入数量: {len(embeddings)}")
        print(f"嵌入维度: {len(embeddings[0]) if embeddings else 0}")
        
        # 记录完成后的显存状态
        embedding_service.log_memory_usage("嵌入完成后")
        
        print_separator("4. 测试手动显存清理")
        
        # 测试手动清理
        if cuda_available:
            print("执行轻量级GPU显存清理...")
        else:
            print("执行轻量级内存清理...")
        embedding_service.cleanup_memory(force=False)
        
        if cuda_available:
            print("执行强制GPU显存清理...")
        else:
            print("执行强制内存清理...")
        embedding_service.cleanup_memory(force=True)
        
        print_separator("5. 测试模型重载")
        
        # 测试模型重载
        print("测试模型重载功能...")
        print(f"模型加载状态: {embedding_service.is_model_loaded()}")
        
        embedding_service.reload_model()
        print(f"重载后模型状态: {embedding_service.is_model_loaded()}")
        
        # 重载后再次测试嵌入
        print("重载后测试嵌入...")
        test_embedding = embedding_service.embed_query("测试查询")
        print(f"✅ 重载后嵌入正常，维度: {len(test_embedding)}")
        
        print_separator("6. 测试健康检查")
        
        # 测试健康检查
        health_status = embedding_service.health_check()
        print(f"健康检查结果: {health_status}")
        
        print_separator("7. 最终清理")
        
        # 最终清理
        embedding_service.cleanup()
        
        print_separator("测试完成")
        if cuda_available:
            print("🎉 所有GPU显存管理功能测试通过！")
        else:
            print("🎉 所有CPU内存管理功能测试通过！")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_monitoring():
    """测试显存监控功能"""
    
    print_separator("内存监控功能专项测试")
    
    cuda_available = torch.cuda.is_available()
    mode_name = "GPU显存" if cuda_available else "CPU内存"
    
    try:
        # 创建启用监控的服务
        service = BGEEmbeddingService(memory_monitor=True)
        
        # 多次执行嵌入，观察显存变化
        for i in range(3):
            print(f"\n--- 第 {i+1} 轮嵌入测试 ---")
            
            service.log_memory_usage(f"第{i+1}轮开始")
            
            texts = [f"测试文本 {j}" for j in range(20)]
            embeddings = service.embed_documents(texts)
            
            service.log_memory_usage(f"第{i+1}轮完成")
            
            # 每轮后清理
            service.cleanup_memory()
            
            time.sleep(1)  # 短暂等待
        
        print(f"\n✅ {mode_name}监控测试完成")
        service.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"❌ {mode_name}监控测试失败: {e}")
        return False


def main():
    """主函数"""
    
    cuda_available = torch.cuda.is_available()
    mode_name = "GPU显存" if cuda_available else "CPU内存"
    
    print(f"🚀 开始{mode_name}管理功能测试")
    print(f"项目根目录: {project_root}")
    print(f"CUDA环境: {'可用' if cuda_available else '不可用'}")
    
    # 执行测试
    test1_result = test_gpu_memory_functionality()
    test2_result = test_memory_monitoring()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"{mode_name}管理功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"{mode_name}监控功能测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有测试通过！{mode_name}管理功能正常工作。")
        return 0
    else:
        print(f"\n❌ 存在测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 