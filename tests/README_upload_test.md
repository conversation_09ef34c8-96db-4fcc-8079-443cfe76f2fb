# Upload API 测试脚本使用说明

## 概述

`test_upload_api.py` 是一个专门测试向量服务 `/upload` API 的测试脚本，可以直接运行，无需复杂配置。

## 前置条件

1. **启动向量服务**：
   ```bash
   python services/vector_service/main.py
   ```

2. **确保依赖包已安装**：
   ```bash
   pip install requests pandas openpyxl
   ```

## 使用方法

### 1. 运行综合测试

```bash
python tests/test_upload_api.py
```

这将自动执行以下测试：
- ✅ 服务健康检查
- ✅ 创建测试CSV文件并上传
- ✅ 创建测试Excel文件并上传（带参数）
- ✅ 文件状态查询
- ✅ 文件列表获取
- ✅ 错误处理测试（无效文件格式）
- ✅ 自动清理临时文件

### 2. 测试指定文件

```bash
python tests/test_upload_api.py --file path/to/your/file.csv
```

### 3. 指定服务地址

```bash
python tests/test_upload_api.py --url http://*************:8000
```

## 测试内容

### 📄 CSV文件测试
- 创建包含公司风险数据的CSV文件
- 测试基本上传功能
- 验证文件处理状态

### 📊 Excel文件测试  
- 创建包含企业风险评估的Excel文件
- 测试带参数的上传（`force_reprocess=True`, `chunk_size=2000`）
- 验证文件处理完成

### 🔍 状态查询测试
- 测试 `/status/{file_code}` 接口
- 实时监控文件处理进度
- 自动等待处理完成

### 📋 文件管理测试
- 测试 `/upload/list` 接口
- 显示已上传文件列表
- 展示文件状态信息

### ❌ 错误处理测试
- 测试无效文件格式上传
- 验证错误响应格式
- 确认服务正确拒绝无效请求

## 输出示例

```
🚀 开始向量服务 /upload API 综合测试
============================================================
🔧 初始化测试器，服务地址: http://localhost:8000
✅ 向量服务健康检查通过
📄 创建测试CSV文件: /tmp/tmpxxx.csv
📊 创建测试Excel文件: /tmp/tmpxxx.xlsx

==================== CSV文件上传测试 ====================

📤 开始上传文件: tmpxxx.csv
📡 响应状态码: 200
✅ 上传成功!
   文件编码: tmpxxx_20241xxx_abc12345
   文件名: tmpxxx.csv
   文件大小: 1024 bytes
   处理状态: PROCESSING
   消息: File uploaded successfully, processing started

⏳ 等待文件处理完成 (最多等待 30 秒)...
🔍 查询文件状态: tmpxxx_20241xxx_abc12345
✅ 状态查询成功!
   文件编码: tmpxxx_20241xxx_abc12345
   文件名: tmpxxx.csv
   状态: COMPLETED
   文档数量: 5
   创建时间: 2024-xx-xx xx:xx:xx
   更新时间: 2024-xx-xx xx:xx:xx
✅ 文件处理完成!

==================== 测试完成 ====================
✅ 向量服务 /upload API 测试完成!
```

## 功能特点

### 🎯 简单易用
- 一键运行，无需复杂配置
- 自动创建测试数据
- 自动清理临时文件

### 🔄 完整流程
- 涵盖上传、状态查询、文件管理全流程
- 支持参数传递测试
- 包含错误处理验证

### 📊 实用数据
- 使用真实的企业风险数据格式
- 包含中文内容测试
- 支持CSV和Excel多种格式

### 🚦 状态监控
- 实时显示处理进度
- 自动等待处理完成
- 清晰的状态反馈

## 故障排除

### 问题1：无法连接服务
```
❌ 无法连接到服务: Connection refused
💡 请确保向量服务已启动: python services/vector_service/main.py
```
**解决方案**：检查向量服务是否正常启动

### 问题2：服务不健康
```
❌ 服务不健康，状态码: 500
```
**解决方案**：检查服务日志，确认模型加载正常

### 问题3：文件上传失败
```
❌ 上传失败: 400
   错误详情: {"detail": "Unsupported file type: .txt"}
```
**解决方案**：检查文件格式是否为支持的类型（.csv, .xlsx, .xls）

## 扩展使用

### 测试自定义文件
```python
from tests.test_upload_api import UploadAPITester

tester = UploadAPITester()
result = tester.upload_file("your_file.csv", force_reprocess=True)
```

### 批量测试
```bash
for file in *.csv; do
    python tests/test_upload_api.py --file "$file"
done
```

## 注意事项

1. **服务依赖**：需要向量服务处于运行状态
2. **文件格式**：仅支持 .csv, .xlsx, .xls 格式
3. **网络超时**：上传请求超时时间为30秒
4. **临时文件**：测试会自动清理创建的临时文件
5. **并发限制**：建议单线程运行，避免资源冲突 