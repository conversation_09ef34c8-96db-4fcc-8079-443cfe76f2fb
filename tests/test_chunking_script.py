#!/usr/bin/env python3
"""
向量化分块测试脚本
支持真实文件测试，可视化分块效果
"""

import os
import sys
import pandas as pd
import argparse
from pathlib import Path

# 添加项目路径
sys.path.append('/')

from services.worker_service.core.vectorizer import VectorizationProcessor, CHUNK_CONFIG

def analyze_chunking_results(documents, file_path):
    """分析分块结果"""
    print(f"\n📊 分块结果分析 - {Path(file_path).name}")
    print("=" * 60)
    
    # 基本统计
    chunk_sizes = [len(doc.page_content) for doc in documents]
    rows_per_chunk = [doc.metadata.get('rows_in_chunk', 0) for doc in documents]
    
    print(f"📈 基本统计:")
    print(f"   总块数: {len(documents)}")
    print(f"   块大小范围: {min(chunk_sizes)} - {max(chunk_sizes)} 字符")
    print(f"   平均块大小: {sum(chunk_sizes) / len(chunk_sizes):.0f} 字符")
    print(f"   每块行数范围: {min(rows_per_chunk)} - {max(rows_per_chunk)} 行")
    print(f"   平均每块行数: {sum(rows_per_chunk) / len(rows_per_chunk):.1f} 行")
    
    # 大小分布分析
    print(f"\n📊 块大小分布:")
    size_distribution = {}
    for size in chunk_sizes:
        kb_range = size // 1000  # 按KB分组
        size_distribution[kb_range] = size_distribution.get(kb_range, 0) + 1
    
    for kb, count in sorted(size_distribution.items()):
        percentage = (count / len(documents)) * 100
        bar = "█" * int(percentage / 5)  # 简单的条形图
        print(f"   {kb}KB-{kb+1}KB: {count:3d} 个块 ({percentage:5.1f}%) {bar}")
    
    # 行数分布分析
    print(f"\n📋 行数分布:")
    row_distribution = {}
    for rows in rows_per_chunk:
        row_distribution[rows] = row_distribution.get(rows, 0) + 1
    
    for rows, count in sorted(row_distribution.items()):
        percentage = (count / len(documents)) * 100
        bar = "█" * int(percentage / 5)
        print(f"   {rows:2d} 行: {count:3d} 个块 ({percentage:5.1f}%) {bar}")
    
    # 质量评估
    print(f"\n🎯 质量评估:")
    target_min = CHUNK_CONFIG["min_chunk_size_chars"]
    target_max = CHUNK_CONFIG["max_chunk_size_chars"]
    
    in_range_count = sum(1 for size in chunk_sizes if target_min <= size <= target_max)
    quality_percentage = (in_range_count / len(chunk_sizes)) * 100
    
    print(f"   目标范围: {target_min} - {target_max} 字符")
    print(f"   在范围内: {in_range_count}/{len(chunk_sizes)} ({quality_percentage:.1f}%)")
    
    # 多样性评估
    unique_row_counts = len(set(rows_per_chunk))
    print(f"   行数多样性: {unique_row_counts} 种不同值")
    
    # 数据完整性
    total_processed_rows = sum(rows_per_chunk)
    print(f"   处理行数: {total_processed_rows}")
    
    return {
        'chunk_count': len(documents),
        'chunk_sizes': chunk_sizes,
        'rows_per_chunk': rows_per_chunk,
        'quality_percentage': quality_percentage,
        'diversity': unique_row_counts,
        'total_processed_rows': total_processed_rows
    }

def show_sample_chunks(documents, num_samples=3):
    """显示示例块内容"""
    print(f"\n📝 示例块内容 (显示前{num_samples}个块):")
    print("=" * 60)
    
    for i, doc in enumerate(documents[:num_samples]):
        print(f"\n🔸 块 {i+1} (ID: {doc.metadata.get('chunk_id', 'N/A')}):")
        print(f"   行范围: {doc.metadata.get('chunk_start_row', 'N/A')} - {doc.metadata.get('chunk_end_row', 'N/A')}")
        print(f"   行数: {doc.metadata.get('rows_in_chunk', 'N/A')}")
        print(f"   字符数: {doc.metadata.get('chunk_size_chars', len(doc.page_content))}")
        print(f"   内容预览 (前300字符):")
        print("   " + "-" * 50)
        content_preview = doc.page_content[:300].replace('\n', '\n   ')
        print(f"   {content_preview}")
        if len(doc.page_content) > 300:
            print("   ...")
        print()

def test_file(file_path, show_samples=True, num_samples=3):
    """测试单个文件"""
    print(f"🧪 测试文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        # 检查文件类型
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in ['.csv', '.xlsx', '.xls']:
            print(f"❌ 不支持的文件类型: {file_ext}")
            return None
        
        # 初始化处理器
        processor = VectorizationProcessor()
        
        # 生成文件编码
        file_code = f"test_{Path(file_path).stem}"
        
        # 处理文件
        print(f"📊 开始处理文件...")
        documents = processor.process_file(file_path, file_code)
        
        if not documents:
            print("❌ 处理失败，未生成任何文档块")
            return None
        
        # 分析结果
        results = analyze_chunking_results(documents, file_path)
        
        # 显示示例
        if show_samples:
            show_sample_chunks(documents, num_samples)
        
        return results
        
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='向量化分块测试脚本')
    parser.add_argument('file_path', nargs='?', help='要测试的CSV/Excel文件路径')
    parser.add_argument('--no-samples', action='store_true', help='不显示示例块内容')
    parser.add_argument('--samples', type=int, default=3, help='显示的示例块数量 (默认: 3)')
    
    args = parser.parse_args()
    
    print("🚀 向量化分块测试脚本")
    print(f"📋 当前配置: {CHUNK_CONFIG}")
    print()
    
    if args.file_path:
        # 测试指定文件
        results = test_file(args.file_path, not args.no_samples, args.samples)
        
        if results:
            print(f"\n✅ 测试完成!")
            print(f"   生成 {results['chunk_count']} 个块")
            print(f"   质量评分: {results['quality_percentage']:.1f}%")
            print(f"   多样性: {results['diversity']} 种行数")
    else:
        # 交互模式
        print("📁 交互模式 - 请输入文件路径 (输入 'quit' 退出):")
        
        while True:
            try:
                file_path = input("\n请输入文件路径: ").strip()
                
                if file_path.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                
                if not file_path:
                    continue
                
                # 移除引号（如果有）
                file_path = file_path.strip('"\'')
                
                results = test_file(file_path, not args.no_samples, args.samples)
                
                if results:
                    print(f"\n✅ 测试完成! 质量评分: {results['quality_percentage']:.1f}%")
                
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
