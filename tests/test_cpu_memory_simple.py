#!/usr/bin/env python3
"""
CPU环境下的内存管理功能简单测试
适用于没有CUDA的环境（如MacBook）
"""

import os
import sys
import gc
from pathlib import Path

# 强制使用CPU模式
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"

def test_imports():
    """测试导入"""
    print("🔄 测试模块导入...")
    
    try:
        import torch
        print(f"✅ PyTorch导入成功: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        from services.vector_service.core.embeddings import BGEEmbeddingService
        print("✅ BGEEmbeddingService导入成功")
        
        from services.vector_service.core.vector_manager import VectorManager  
        print("✅ VectorManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_memory_info():
    """测试内存信息获取"""
    print("\n🔄 测试内存信息获取...")
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        # 创建服务实例（不加载模型）
        service = BGEEmbeddingService.__new__(BGEEmbeddingService)
        service.device = "cpu"
        service.memory_monitor = True
        
        # 测试内存信息获取
        memory_info = service.get_memory_info()
        print(f"内存信息: {memory_info}")
        
        # 检查是否正确识别为CPU模式
        if memory_info.get("mode") == "CPU":
            print("✅ 正确识别为CPU模式")
        else:
            print("⚠️  CPU模式识别可能有问题")
            
        return True
        
    except Exception as e:
        print(f"❌ 内存信息测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cleanup_functions():
    """测试清理功能"""
    print("\n🔄 测试清理功能...")
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        # 创建服务实例（不加载模型）
        service = BGEEmbeddingService.__new__(BGEEmbeddingService)
        service.device = "cpu"
        service.memory_monitor = True
        service.auto_cleanup = True
        service.model = None
        service.embedding_cache = {}
        
        # 测试清理函数
        print("测试cleanup_memory...")
        service.cleanup_memory(force=False)
        print("✅ cleanup_memory执行成功")
        
        print("测试cleanup...")
        service.cleanup()
        print("✅ cleanup执行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config():
    """测试配置"""
    print("\n🔄 测试配置...")
    
    try:
        from services.vector_service.config import config
        
        print(f"自动清理: {config.auto_cleanup}")
        print(f"内存监控: {config.memory_monitor}")
        print(f"清理阈值: {config.cleanup_threshold}")
        print(f"批次清理间隔: {config.batch_cleanup_interval}")
        
        print("✅ 配置读取成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🚀 CPU环境内存管理功能简单测试")
    print(f"项目根目录: {project_root}")
    print("=" * 50)
    
    # 执行测试
    results = []
    
    results.append(("导入测试", test_imports()))
    results.append(("配置测试", test_config()))
    results.append(("内存信息测试", test_memory_info()))
    results.append(("清理功能测试", test_cleanup_functions()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有基础测试通过！CPU环境下的内存管理功能可以正常工作。")
        print("\n📝 注意：")
        print("- 此测试仅验证基础功能，不包括实际的模型加载和嵌入生成")
        print("- 在实际部署时，请运行完整的向量化服务进行测试")
        print("- GPU环境下的显存管理功能需要在CUDA环境中测试")
        return 0
    else:
        print("❌ 存在测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 