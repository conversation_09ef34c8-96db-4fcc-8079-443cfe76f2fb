#!/usr/bin/env python3
"""
简单测试修正后的vector_service架构
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "services" / "vector_service"))

from services.vector_service.config import config
from shared.utils.logger import get_logger

logger = get_logger(__name__)


def test_config():
    """测试配置"""
    try:
        logger.info("测试配置...")
        logger.info(f"服务名称: {config.service_name}")
        logger.info(f"ChromaDB持久化目录: {config.chroma_persist_dir}")
        logger.info(f"缓存目录: {config.cache_dir}")
        logger.info(f"模型名称: {config.model_name}")
        logger.info("✅ 配置测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    try:
        logger.info("测试目录结构...")
        
        # 创建测试目录
        test_persist_dir = Path("./test_cache/chroma_db")
        test_persist_dir.mkdir(parents=True, exist_ok=True)
        
        # 测试文件编码目录
        test_file_code = "test_001"
        file_persist_dir = test_persist_dir / test_file_code
        file_persist_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"基础目录: {test_persist_dir}")
        logger.info(f"文件目录: {file_persist_dir}")
        
        # 检查目录是否存在
        assert test_persist_dir.exists(), "基础目录不存在"
        assert file_persist_dir.exists(), "文件目录不存在"
        
        logger.info("✅ 目录结构测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 目录结构测试失败: {e}")
        return False


def test_vector_manager_init():
    """测试向量管理器初始化"""
    try:
        logger.info("测试向量管理器初始化...")

        # 创建一个模拟的嵌入服务类
        class MockEmbeddingService:
            def health_check(self):
                return True

        # 不使用实际的嵌入服务，只测试初始化
        from services.vector_service.core.vector_manager import VectorManager

        # 创建向量管理器（使用模拟嵌入服务）
        vector_manager = VectorManager(
            base_persist_dir="./test_cache/chroma_db",
            embedding_service=MockEmbeddingService()
        )

        # 测试基本方法
        collection_name = vector_manager.get_collection_name_by_code("test_001")
        persist_dir = vector_manager.get_persist_dir_by_code("test_001")

        logger.info(f"集合名称: {collection_name}")
        logger.info(f"持久化目录: {persist_dir}")

        assert collection_name == "risk_analysis_test_001", "集合名称不正确"
        assert "test_001" in persist_dir, "持久化目录不正确"

        # 测试健康检查
        health = vector_manager.health_check()
        logger.info(f"健康检查结果: {health}")

        logger.info("✅ 向量管理器初始化测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 向量管理器初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    logger.info("开始架构修正验证测试...")
    
    tests = [
        ("配置测试", test_config),
        ("目录结构测试", test_directory_structure),
        ("向量管理器初始化测试", test_vector_manager_init),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
        
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！架构修正成功！")
    else:
        logger.error("❌ 部分测试失败，需要进一步修正")


if __name__ == "__main__":
    main()
