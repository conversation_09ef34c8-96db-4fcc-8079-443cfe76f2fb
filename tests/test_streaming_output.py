#!/usr/bin/env python3
"""
测试流式输出功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import settings
from src.risk_analyzer import RiskAnalyzer
from src.file_coded_vector_store import FileCodedVectorStore


def test_streaming_output():
    """测试流式输出功能"""

    print("=== 流式输出功能测试 ===\n")

    # 初始化向量存储
    vector_store = FileCodedVectorStore()

    # 获取所有可用的文件编码
    file_codes = vector_store.list_file_codes()

    if not file_codes:
        print("❌ 没有找到可用的文件编码，请先上传一些Excel文件")
        return

    print(f"找到 {len(file_codes)} 个文件编码: {file_codes[:3]}...")

    # 选择第一个文件编码进行测试
    test_file_code = file_codes[0]
    print(f"使用文件编码进行测试: {test_file_code}\n")

    # 测试1: 非流式输出（默认配置）
    print("🔄 测试1: 非流式输出模式")
    print("-" * 50)

    # 确保流式输出关闭
    original_streaming = settings.ENABLE_STREAMING_OUTPUT
    settings.ENABLE_STREAMING_OUTPUT = False

    analyzer_non_stream = RiskAnalyzer()

    print("开始非流式分析...")
    result_non_stream = analyzer_non_stream.generate_risk_report(test_file_code)

    if result_non_stream.get("success"):
        print(f"✅ 非流式分析完成")
        print(f"违约概率: {result_non_stream['default_probability']:.2%}")
        print(f"风险等级: {result_non_stream['risk_level']}")
        print(f"分析摘要长度: {len(result_non_stream['analysis_summary'])} 字符")
        print(f"摘要: {result_non_stream['analysis_summary']}")
    else:
        print(f"❌ 非流式分析失败: {result_non_stream.get('error')}")

    print("\n" + "=" * 60 + "\n")

    # 测试2: 流式输出
    print("🔄 测试2: 流式输出模式")
    print("-" * 50)

    # 启用流式输出
    settings.ENABLE_STREAMING_OUTPUT = True

    analyzer_stream = RiskAnalyzer()

    print("开始流式分析...")
    print("流式输出内容:")
    print("-" * 30)

    result_stream = analyzer_stream.generate_risk_report(test_file_code)

    print("-" * 30)

    if result_stream.get("success"):
        print(f"✅ 流式分析完成")
        print(f"违约概率: {result_stream['default_probability']:.2%}")
        print(f"风险等级: {result_stream['risk_level']}")
        print(f"分析摘要长度: {len(result_stream['analysis_summary'])} 字符")
    else:
        print(f"❌ 流式分析失败: {result_stream.get('error')}")

    # 恢复原始配置
    settings.ENABLE_STREAMING_OUTPUT = original_streaming

    print("\n" + "=" * 60 + "\n")

    # 比较结果
    if result_non_stream.get("success") and result_stream.get("success"):
        print("📊 结果比较:")
        print(f"非流式违约概率: {result_non_stream['default_probability']:.4f}")
        print(f"流式违约概率: {result_stream['default_probability']:.4f}")
        print(
            f"概率差异: {abs(result_non_stream['default_probability'] - result_stream['default_probability']):.4f}"
        )

        if (
            abs(
                result_non_stream["default_probability"]
                - result_stream["default_probability"]
            )
            < 0.01
        ):
            print("✅ 两种模式结果基本一致")
        else:
            print("⚠️ 两种模式结果存在差异")

    print("\n🎉 流式输出功能测试完成!")


if __name__ == "__main__":
    test_streaming_output()
