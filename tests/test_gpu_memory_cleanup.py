#!/usr/bin/env python3
"""
GPU显存自动清理功能测试脚本
验证embedding服务在处理向量化后是否正确释放GPU显存
"""

import sys
import time
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from services.embedding_service.core.bge_embedding import BGEEmbeddingService

logger = get_logger(__name__)


def print_separator(title: str):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f"📋 {title}")
    print("=" * 60)


def get_gpu_memory_info():
    """获取GPU显存信息"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        reserved = torch.cuda.memory_reserved() / 1024**3  # GB
        return {
            "allocated_gb": round(allocated, 3),
            "reserved_gb": round(reserved, 3),
            "available": True
        }
    else:
        return {"available": False}


def test_automatic_gpu_cleanup():
    """测试自动GPU显存清理功能"""
    
    print_separator("自动GPU显存清理功能测试")
    
    cuda_available = torch.cuda.is_available()
    if not cuda_available:
        print("⚠️  CUDA不可用，将测试CPU模式的内存管理")
    else:
        print("✅ CUDA可用，将测试GPU显存自动清理")
    
    try:
        print("\n1. 初始化嵌入服务（启用自动清理）")
        
        # 创建启用自动清理的嵌入服务
        embedding_service = BGEEmbeddingService(
            auto_cleanup=True,
            memory_monitor=True,
            batch_size=8  # 使用较小批次便于观察
        )
        
        print("✅ 嵌入服务初始化成功")
        
        # 记录初始显存状态
        initial_memory = get_gpu_memory_info()
        print(f"📊 初始显存状态: {initial_memory}")
        
        print("\n2. 执行多轮向量化测试")
        
        # 准备测试文本
        test_texts = [
            "这是第一个测试文档，用于验证GPU显存自动清理功能。",
            "风险分析系统需要处理大量的金融数据文档。",
            "机器学习模型在处理向量化时会占用大量GPU显存。",
            "自动清理机制应该在每次处理后释放不必要的显存。",
            "BGE-M3模型是一个高性能的文本嵌入模型。"
        ] * 20  # 重复20次，增加显存压力
        
        # 执行多轮测试
        for round_num in range(5):
            print(f"\n--- 第 {round_num + 1} 轮向量化测试 ---")
            
            # 记录处理前显存
            before_memory = get_gpu_memory_info()
            print(f"处理前显存: {before_memory}")
            
            # 执行向量化
            start_time = time.time()
            embeddings = embedding_service.embed_documents(test_texts)
            processing_time = time.time() - start_time
            
            print(f"✅ 向量化完成: {len(embeddings)} 个向量，耗时 {processing_time:.3f}秒")
            
            # 等待自动清理完成
            time.sleep(0.5)
            
            # 记录处理后显存
            after_memory = get_gpu_memory_info()
            print(f"处理后显存: {after_memory}")
            
            # 分析显存变化
            if cuda_available and before_memory["available"] and after_memory["available"]:
                allocated_diff = after_memory["allocated_gb"] - before_memory["allocated_gb"]
                reserved_diff = after_memory["reserved_gb"] - before_memory["reserved_gb"]
                
                print(f"显存变化: 已分配 {allocated_diff:+.3f}GB, 已保留 {reserved_diff:+.3f}GB")
                
                # 检查是否有显存泄漏
                if allocated_diff > 0.1:  # 超过100MB认为可能有泄漏
                    print(f"⚠️  可能存在显存泄漏: 已分配显存增加 {allocated_diff:.3f}GB")
                else:
                    print("✅ 显存清理正常")
        
        print("\n3. 测试手动显存清理")
        
        # 记录手动清理前显存
        before_cleanup = get_gpu_memory_info()
        print(f"手动清理前显存: {before_cleanup}")
        
        # 执行手动显存清理
        embedding_service.cleanup_gpu_memory_only()
        
        # 等待清理完成
        time.sleep(0.2)
        
        # 记录手动清理后显存
        after_cleanup = get_gpu_memory_info()
        print(f"手动清理后显存: {after_cleanup}")
        
        if cuda_available and before_cleanup["available"] and after_cleanup["available"]:
            cleanup_diff = before_cleanup["allocated_gb"] - after_cleanup["allocated_gb"]
            print(f"手动清理释放显存: {cleanup_diff:.3f}GB")
        
        print("\n4. 资源清理")
        embedding_service.cleanup()
        
        # 最终显存状态
        final_memory = get_gpu_memory_info()
        print(f"📊 最终显存状态: {final_memory}")
        
        print("\n✅ 自动GPU显存清理功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 自动GPU显存清理测试失败: {e}")
        return False


def test_memory_cleanup_api():
    """测试显存清理API功能"""
    
    print_separator("显存清理API功能测试")
    
    try:
        # 这里可以添加对API端点的测试
        # 由于需要启动服务，暂时跳过
        print("ℹ️  API测试需要启动embedding服务，请手动测试以下端点：")
        print("   POST /api/v1/embed/memory/cleanup")
        print("   POST /api/v1/embed/memory/cleanup?force=true")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 显存清理API测试失败: {e}")
        return False


def main():
    """主函数"""
    
    cuda_available = torch.cuda.is_available()
    mode_name = "GPU显存" if cuda_available else "CPU内存"
    
    print(f"🚀 开始{mode_name}自动清理功能测试")
    print(f"项目根目录: {project_root}")
    print(f"CUDA环境: {'可用' if cuda_available else '不可用'}")
    
    if cuda_available:
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU设备: {gpu_name}")
        print(f"GPU显存: {gpu_memory:.1f}GB")
    
    # 执行测试
    test1_result = test_automatic_gpu_cleanup()
    test2_result = test_memory_cleanup_api()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"自动{mode_name}清理测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"显存清理API测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有测试通过！{mode_name}自动清理功能正常工作。")
        return 0
    else:
        print(f"\n❌ 存在测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
