#!/usr/bin/env python3
"""
向量服务 /upload API 测试脚本
可以直接运行，测试文件上传功能

使用方法:
1. 启动向量服务: python services/vector_service/main.py
2. 运行此测试: python tests/test_upload_api.py
3. 交互模式: python tests/test_upload_api.py --interactive
"""

import os
import sys
import requests
import json
import pandas as pd
import tempfile
from pathlib import Path
from typing import Dict, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class UploadAPITester:
    """Upload API 测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化测试器
        
        Args:
            base_url: 向量服务基础URL
        """
        self.base_url = base_url
        self.upload_url = f"{base_url}/upload/"
        self.status_url = f"{base_url}/status"
        self.list_url = f"{base_url}/status/files"
        self.session = requests.Session()
        
        print(f"🔧 初始化测试器，服务地址: {base_url}")
    
    def check_service_health(self) -> bool:
        """检查服务是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ 向量服务健康检查通过")
                return True
            else:
                print(f"❌ 服务不健康，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            print("💡 请确保向量服务已启动: python services/vector_service/main.py")
            return False
    
    def create_test_csv(self) -> str:
        """创建测试用的CSV文件"""
        # 创建临时CSV文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        
        # 写入测试数据
        test_data = {
            '公司名称': ['阿里巴巴', '腾讯控股', '字节跳动', '美团', '京东'],
            '行业': ['电子商务', '社交媒体', '互联网', '外卖服务', '电子商务'],
            '风险等级': ['中等', '低', '中等', '高', '中等'],
            '营收(亿)': [5097, 5545, 3900, 1749, 9515],
            '员工数': [245000, 112771, 150000, 570000, 400000],
            '风险描述': [
                '市场竞争激烈，监管政策变化风险',
                '内容审查风险，游戏业务监管加强',
                '字节跳动面临国际业务合规挑战',
                '外卖行业竞争激烈，盈利压力大',
                '供应链管理复杂，物流成本上升'
            ]
        }
        
        df = pd.DataFrame(test_data)
        df.to_csv(temp_file.name, index=False, encoding='utf-8')
        temp_file.close()
        
        print(f"📄 创建测试CSV文件: {temp_file.name}")
        return temp_file.name
    
    def create_test_excel(self) -> str:
        """创建测试用的Excel文件"""
        # 创建临时Excel文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_file.close()
        
        # 写入测试数据
        test_data = {
            '企业ID': ['E001', 'E002', 'E003', 'E004', 'E005'],
            '企业名称': ['科技公司A', '制造企业B', '金融机构C', '零售集团D', '服务公司E'],
            '注册资本(万)': [5000, 12000, 50000, 8000, 3000],
            '成立年份': [2015, 2010, 2005, 2018, 2020],
            '主要风险': [
                '技术更新换代快，研发投入大',
                '原材料价格波动，环保压力增加',
                '金融监管严格，市场风险较高',
                '消费习惯变化，线上竞争激烈',
                '人员流动性大，服务质量不稳定'
            ],
            '风险评分': [7.5, 6.2, 8.1, 5.8, 6.9]
        }
        
        df = pd.DataFrame(test_data)
        with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='企业风险数据', index=False)
        
        print(f"📊 创建测试Excel文件: {temp_file.name}")
        return temp_file.name
    
    def upload_file(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        上传文件到向量服务
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数(force_reprocess, chunk_size, chunk_overlap)
            
        Returns:
            上传结果
        """
        try:
            print(f"\n📤 开始上传文件: {Path(file_path).name}")
            
            # 准备文件
            with open(file_path, 'rb') as f:
                files = {'file': (Path(file_path).name, f, 'application/octet-stream')}
                
                # 准备额外参数
                data = {}
                if 'force_reprocess' in kwargs:
                    data['force_reprocess'] = kwargs['force_reprocess']
                if 'chunk_size' in kwargs:
                    data['chunk_size'] = kwargs['chunk_size']
                if 'chunk_overlap' in kwargs:
                    data['chunk_overlap'] = kwargs['chunk_overlap']
                
                # 发送请求
                response = self.session.post(
                    self.upload_url,
                    files=files,
                    data=data,
                    timeout=30
                )
            
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功!")
                print(f"   文件编码: {result.get('file_code')}")
                print(f"   文件名: {result.get('file_name')}")
                print(f"   文件大小: {result.get('file_size')} bytes")
                print(f"   处理状态: {result.get('status')}")
                print(f"   消息: {result.get('message')}")
                return result
            else:
                print(f"❌ 上传失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   错误内容: {response.text}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"❌ 上传异常: {e}")
            return {"success": False, "error": str(e)}
    
    def check_file_status(self, file_code: str) -> Dict[str, Any]:
        """
        检查文件处理状态
        
        Args:
            file_code: 文件编码
            
        Returns:
            状态信息
        """
        try:
            print(f"\n🔍 查询文件状态: {file_code}")
            response = self.session.get(f"{self.status_url}/files/{file_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 状态查询成功!")
                print(f"   文件编码: {result.get('file_code')}")
                print(f"   文件名: {result.get('filename')}")  # API 返回 filename 不是 file_name
                print(f"   状态: {result.get('status')}")
                print(f"   文档数量: {result.get('document_count')}")
                print(f"   上传时间: {result.get('uploaded_at')}")  # API 返回 uploaded_at 不是 created_at
                print(f"   更新时间: {result.get('updated_at')}")
                return result
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            return {"success": False, "error": str(e)}
    
    def list_files(self) -> Dict[str, Any]:
        """
        获取文件列表
        
        Returns:
            文件列表
        """
        try:
            print(f"\n📋 获取文件列表...")
            response = self.session.get(self.list_url)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 文件列表获取成功!")
                print(f"   文件总数: {result.get('total_files')}")  # API 返回 total_files
                
                files = result.get('files', [])
                for i, file_info in enumerate(files[:10], 1):  # 显示前10个
                    print(f"   {i}. {file_info.get('filename')} (状态: {file_info.get('status')}) [编码: {file_info.get('file_code')}]")  # 使用 filename
                
                if len(files) > 10:
                    print(f"   ... 还有 {len(files) - 10} 个文件")
                
                return result
            else:
                print(f"❌ 文件列表获取失败: {response.status_code}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"❌ 文件列表获取异常: {e}")
            return {"success": False, "error": str(e)}
    
    def wait_for_processing(self, file_code: str, max_wait: int = 60) -> bool:
        """
        等待文件处理完成
        
        Args:
            file_code: 文件编码
            max_wait: 最大等待时间(秒)
            
        Returns:
            是否处理完成
        """
        print(f"\n⏳ 等待文件处理完成 (最多等待 {max_wait} 秒)...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            status_result = self.check_file_status(file_code)
            
            # 检查是否成功查询到状态（有 file_code 字段表示成功）
            if status_result.get('file_code'):
                status = status_result.get('status')
                if status == 'COMPLETED':
                    print(f"✅ 文件处理完成!")
                    return True
                elif status == 'FAILED':
                    print(f"❌ 文件处理失败!")
                    return False
                else:
                    print(f"⏳ 当前状态: {status}, 继续等待...")
                    time.sleep(3)
            else:
                print(f"❌ 状态查询失败，停止等待")
                return False
        
        print(f"⏰ 等待超时，文件可能仍在处理中")
        return False
    
    def cleanup_file(self, file_path: str):
        """清理临时文件"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                print(f"🗑️  清理临时文件: {Path(file_path).name}")
        except Exception as e:
            print(f"⚠️  清理文件失败: {e}")
    
    def get_user_input(self, prompt: str, default: str = "") -> str:
        """获取用户输入"""
        try:
            if default:
                user_input = input(f"{prompt} (默认: {default}): ").strip()
                return user_input if user_input else default
            else:
                return input(f"{prompt}: ").strip()
        except (KeyboardInterrupt, EOFError):
            return ""
    
    def interactive_mode(self):
        """交互模式"""
        print("🎮 进入交互模式")
        print("=" * 60)
        
        # 检查服务健康状态
        if not self.check_service_health():
            print("❌ 服务不可用，退出交互模式")
            return
        
        # 存储创建的测试文件，用于清理
        temp_files = []
        
        try:
            while True:
                print("\n" + "=" * 60)
                print("🎯 请选择要测试的功能:")
                print("1. 🏥 服务健康检查")
                print("2. 📤 上传CSV测试文件")
                print("3. 📊 上传Excel测试文件")
                print("4. 📁 上传自定义文件")
                print("5. 🔍 查询文件状态")
                print("6. 📋 获取文件列表")
                print("7. ⏳ 等待文件处理完成")
                print("8. 🧪 创建测试文件(不上传)")
                print("9. 🗑️  清理临时文件")
                print("0. 🚪 退出交互模式")
                print("=" * 60)
                
                choice = self.get_user_input("请输入选项编号")
                
                if choice == "0":
                    print("👋 退出交互模式")
                    break
                elif choice == "1":
                    print(f"\n{'🏥 服务健康检查':.^40}")
                    self.check_service_health()
                
                elif choice == "2":
                    print(f"\n{'📤 上传CSV测试文件':.^40}")
                    csv_file = self.create_test_csv()
                    temp_files.append(csv_file)
                    
                    # 询问上传参数
                    force_reprocess = self.get_user_input("是否强制重新处理? (y/n)", "n").lower() == 'y'
                    chunk_size = self.get_user_input("分块大小 (留空使用默认值)")
                    chunk_overlap = self.get_user_input("分块重叠 (留空使用默认值)")
                    
                    kwargs = {}
                    if force_reprocess:
                        kwargs['force_reprocess'] = True
                    if chunk_size:
                        try:
                            kwargs['chunk_size'] = int(chunk_size)
                        except ValueError:
                            print("⚠️  分块大小格式错误，使用默认值")
                    if chunk_overlap:
                        try:
                            kwargs['chunk_overlap'] = int(chunk_overlap)
                        except ValueError:
                            print("⚠️  分块重叠格式错误，使用默认值")
                    
                    result = self.upload_file(csv_file, **kwargs)
                    
                    # 询问是否等待处理完成
                    if result.get('success'):
                        wait = self.get_user_input("是否等待处理完成? (y/n)", "y").lower() == 'y'
                        if wait:
                            file_code = result.get('file_code')
                            self.wait_for_processing(file_code, max_wait=60)
                
                elif choice == "3":
                    print(f"\n{'📊 上传Excel测试文件':.^40}")
                    excel_file = self.create_test_excel()
                    temp_files.append(excel_file)
                    
                    # 询问上传参数
                    force_reprocess = self.get_user_input("是否强制重新处理? (y/n)", "n").lower() == 'y'
                    chunk_size = self.get_user_input("分块大小 (留空使用默认值)")
                    chunk_overlap = self.get_user_input("分块重叠 (留空使用默认值)")
                    
                    kwargs = {}
                    if force_reprocess:
                        kwargs['force_reprocess'] = True
                    if chunk_size:
                        try:
                            kwargs['chunk_size'] = int(chunk_size)
                        except ValueError:
                            print("⚠️  分块大小格式错误，使用默认值")
                    if chunk_overlap:
                        try:
                            kwargs['chunk_overlap'] = int(chunk_overlap)
                        except ValueError:
                            print("⚠️  分块重叠格式错误，使用默认值")
                    
                    result = self.upload_file(excel_file, **kwargs)
                    
                    # 询问是否等待处理完成
                    if result.get('success'):
                        wait = self.get_user_input("是否等待处理完成? (y/n)", "y").lower() == 'y'
                        if wait:
                            file_code = result.get('file_code')
                            self.wait_for_processing(file_code, max_wait=60)
                
                elif choice == "4":
                    print(f"\n{'📁 上传自定义文件':.^40}")
                    file_path = self.get_user_input("请输入文件路径")
                    
                    if not file_path or not os.path.exists(file_path):
                        print("❌ 文件路径无效或文件不存在")
                        continue
                    
                    # 询问上传参数
                    force_reprocess = self.get_user_input("是否强制重新处理? (y/n)", "n").lower() == 'y'
                    chunk_size = self.get_user_input("分块大小 (留空使用默认值)")
                    chunk_overlap = self.get_user_input("分块重叠 (留空使用默认值)")
                    
                    kwargs = {}
                    if force_reprocess:
                        kwargs['force_reprocess'] = True
                    if chunk_size:
                        try:
                            kwargs['chunk_size'] = int(chunk_size)
                        except ValueError:
                            print("⚠️  分块大小格式错误，使用默认值")
                    if chunk_overlap:
                        try:
                            kwargs['chunk_overlap'] = int(chunk_overlap)
                        except ValueError:
                            print("⚠️  分块重叠格式错误，使用默认值")
                    
                    result = self.upload_file(file_path, **kwargs)
                    
                    # 询问是否等待处理完成
                    if result.get('success'):
                        wait = self.get_user_input("是否等待处理完成? (y/n)", "y").lower() == 'y'
                        if wait:
                            file_code = result.get('file_code')
                            self.wait_for_processing(file_code, max_wait=60)
                
                elif choice == "5":
                    print(f"\n{'🔍 查询文件状态':.^40}")
                    file_code = self.get_user_input("请输入文件编码")
                    
                    if not file_code:
                        print("❌ 文件编码不能为空")
                        continue
                    
                    self.check_file_status(file_code)
                
                elif choice == "6":
                    print(f"\n{'📋 获取文件列表':.^40}")
                    self.list_files()
                
                elif choice == "7":
                    print(f"\n{'⏳ 等待文件处理完成':.^40}")
                    file_code = self.get_user_input("请输入文件编码")
                    
                    if not file_code:
                        print("❌ 文件编码不能为空")
                        continue
                    
                    max_wait = self.get_user_input("最大等待时间(秒)", "60")
                    try:
                        max_wait = int(max_wait)
                    except ValueError:
                        max_wait = 60
                        print("⚠️  等待时间格式错误，使用默认值60秒")
                    
                    self.wait_for_processing(file_code, max_wait=max_wait)
                
                elif choice == "8":
                    print(f"\n{'🧪 创建测试文件':.^40}")
                    print("1. 创建CSV测试文件")
                    print("2. 创建Excel测试文件")
                    
                    file_choice = self.get_user_input("请选择文件类型 (1/2)")
                    
                    if file_choice == "1":
                        csv_file = self.create_test_csv()
                        temp_files.append(csv_file)
                    elif file_choice == "2":
                        excel_file = self.create_test_excel()
                        temp_files.append(excel_file)
                    else:
                        print("❌ 无效选择")
                
                elif choice == "9":
                    print(f"\n{'🗑️ 清理临时文件':.^40}")
                    if temp_files:
                        print(f"找到 {len(temp_files)} 个临时文件:")
                        for i, file_path in enumerate(temp_files, 1):
                            print(f"   {i}. {Path(file_path).name}")
                        
                        clean_all = self.get_user_input("是否清理所有临时文件? (y/n)", "y").lower() == 'y'
                        
                        if clean_all:
                            for file_path in temp_files[:]:
                                self.cleanup_file(file_path)
                                temp_files.remove(file_path)
                        else:
                            # 选择性清理
                            index = self.get_user_input("请输入要清理的文件编号 (1-{})".format(len(temp_files)))
                            try:
                                index = int(index) - 1
                                if 0 <= index < len(temp_files):
                                    file_path = temp_files.pop(index)
                                    self.cleanup_file(file_path)
                                else:
                                    print("❌ 无效的文件编号")
                            except ValueError:
                                print("❌ 请输入有效的数字")
                    else:
                        print("💡 没有临时文件需要清理")
                
                else:
                    print("❌ 无效选择，请重新输入")
                
                # 询问是否继续
                if choice != "0":
                    continue_test = self.get_user_input("\n按回车继续，输入q退出", "").lower()
                    if continue_test == 'q':
                        print("👋 退出交互模式")
                        break
        
        except (KeyboardInterrupt, EOFError):
            print("\n\n👋 用户中断，退出交互模式")
        
        finally:
            # 清理所有临时文件
            if temp_files:
                print(f"\n🗑️  清理 {len(temp_files)} 个临时文件...")
                for file_path in temp_files:
                    self.cleanup_file(file_path)
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始向量服务 /upload API 综合测试")
        print("=" * 60)
        
        # 1. 检查服务健康状态
        if not self.check_service_health():
            return False
        
        # 2. 创建测试文件
        csv_file = None
        excel_file = None
        
        try:
            csv_file = self.create_test_csv()
            excel_file = self.create_test_excel()
            
            # 3. 测试CSV文件上传
            print(f"\n{'=' * 20} CSV文件上传测试 {'=' * 20}")
            csv_result = self.upload_file(csv_file)
            
            if csv_result.get('success'):
                csv_file_code = csv_result.get('file_code')
                
                # 等待处理完成
                self.wait_for_processing(csv_file_code, max_wait=30)
            
            # 4. 测试Excel文件上传
            print(f"\n{'=' * 20} Excel文件上传测试 {'=' * 20}")
            excel_result = self.upload_file(excel_file, force_reprocess=True, chunk_size=2000)
            
            if excel_result.get('success'):
                excel_file_code = excel_result.get('file_code')
                
                # 等待处理完成
                self.wait_for_processing(excel_file_code, max_wait=30)
            
            # 5. 测试文件列表
            print(f"\n{'=' * 20} 文件列表测试 {'=' * 20}")
            self.list_files()
            
            # 6. 测试错误情况 - 无效文件
            print(f"\n{'=' * 20} 错误处理测试 {'=' * 20}")
            # 创建无效文件
            invalid_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
            invalid_file.write("这是一个无效的文件格式")
            invalid_file.close()
            
            print("测试无效文件格式上传...")
            invalid_result = self.upload_file(invalid_file.name)
            
            if not invalid_result.get('success'):
                print("✅ 正确拒绝了无效文件格式")
            else:
                print("⚠️  意外接受了无效文件格式")
            
            self.cleanup_file(invalid_file.name)
            
            print(f"\n{'=' * 20} 测试完成 {'=' * 20}")
            print("✅ 向量服务 /upload API 测试完成!")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            return False
            
        finally:
            # 清理测试文件
            if csv_file:
                self.cleanup_file(csv_file)
            if excel_file:
                self.cleanup_file(excel_file)


def main():
    """主函数"""
    # 检查命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='向量服务 Upload API 测试')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='向量服务URL (默认: http://localhost:8000)')
    parser.add_argument('--file', type=str, 
                       help='测试指定文件上传')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='进入交互模式')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = UploadAPITester(base_url=args.url)
    
    if args.interactive:
        # 交互模式
        tester.interactive_mode()
    elif args.file:
        # 测试指定文件
        print(f"🎯 测试指定文件: {args.file}")
        if not tester.check_service_health():
            return
        
        result = tester.upload_file(args.file)
        if result.get('success'):
            file_code = result.get('file_code')
            tester.wait_for_processing(file_code)
    else:
        # 运行综合测试
        tester.run_comprehensive_test()


if __name__ == "__main__":
    main() 