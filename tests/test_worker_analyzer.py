#!/usr/bin/env python3
"""
测试Worker服务分析器的修复
验证embedding service API调用是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from services.worker_service.core.analyzer import RiskAnalyzer
    from services.worker_service.config import config
    from shared.utils.logger import get_logger
    
    HAS_DEPS = True
except ImportError as e:
    HAS_DEPS = False
    print(f"依赖库导入失败: {e}")

logger = get_logger(__name__) if HAS_DEPS else None

def test_analyzer_embedding():
    """测试分析器的嵌入服务调用"""
    if not HAS_DEPS:
        print("❌ 依赖库未正确导入")
        return False
    
    try:
        print("🔍 测试Worker服务分析器的嵌入修复")
        
        # 初始化分析器
        print("初始化风险分析器...")
        analyzer = RiskAnalyzer()
        print("✅ 风险分析器初始化成功")
        
        # 测试HTTP embedding请求
        print("测试HTTP embedding请求...")
        try:
            test_embedding = analyzer._get_embedding_via_http("测试查询")
            if test_embedding:
                print(f"✅ HTTP embedding请求工作正常，维度: {len(test_embedding)}")
            else:
                print("❌ HTTP embedding请求返回空结果")
                return False
        except Exception as embed_error:
            print(f"❌ HTTP embedding请求测试失败: {embed_error}")
            return False
        
        # 获取可用的文件编码
        print("获取可用文件编码...")
        try:
            collections = analyzer.chroma_client.list_collections()
            if not collections:
                print("⚠️ 没有找到任何集合，请先运行向量化步骤")
                return False
            
            # 选择第一个集合进行测试
            test_collection = collections[0]
            collection_name = test_collection.name
            
            # 提取文件编码
            if collection_name.startswith(config.chroma_collection_prefix):
                file_code = collection_name[len(config.chroma_collection_prefix):]
                print(f"测试文件编码: {file_code}")
            else:
                print(f"❌ 集合名称格式不正确: {collection_name}")
                return False
            
        except Exception as collection_error:
            print(f"❌ 获取集合失败: {collection_error}")
            return False
        
        # 测试获取所有文档的方法
        print(f"测试获取文件 {file_code} 的所有文档...")
        try:
            documents = analyzer._get_all_documents_by_code(file_code)
            print(f"✅ 成功获取 {len(documents)} 个文档")
            
            if documents:
                first_doc = documents[0]
                print(f"第一个文档预览: {first_doc.get('content', '')[:100]}...")
                print(f"文档相似度: {first_doc.get('similarity', 0):.3f}")
            
            return True
            
        except Exception as doc_error:
            print(f"❌ 获取文档失败: {doc_error}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Worker服务分析器嵌入修复测试")
    print("=" * 50)
    
    # 运行测试
    success = test_analyzer_embedding()
    
    if success:
        print("\n✅ 测试通过！Worker服务分析器修复成功")
        print("现在可以正常运行风险分析任务")
    else:
        print("\n❌ 测试失败，请检查embedding service和ChromaDB服务状态")
    
    return success

if __name__ == "__main__":
    main()
