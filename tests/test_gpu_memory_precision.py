#!/usr/bin/env python3
"""
精确GPU显存泄漏测试脚本

用于测试修复后的向量化服务是否完全解决了显存泄漏问题。
包含高精度显存监控和多轮测试验证。
"""

import os
import sys
import time
import gc
import torch
import requests
import json
from pathlib import Path
from typing import Dict, List, Any
import tempfile
import pandas as pd

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def precise_memory_check() -> Dict[str, float]:
    """
    精确的GPU显存检查
    
    Returns:
        显存信息字典
    """
    if not torch.cuda.is_available():
        return {"error": "CUDA不可用"}
    
    # 同步GPU操作
    torch.cuda.synchronize()
    
    # 多次读取取平均值
    allocated_values = []
    reserved_values = []
    
    for _ in range(5):
        allocated_values.append(torch.cuda.memory_allocated() / 1024**3)
        reserved_values.append(torch.cuda.memory_reserved() / 1024**3)
        torch.cuda.synchronize()
        time.sleep(0.01)  # 10ms间隔
    
    return {
        "allocated_gb": sum(allocated_values) / len(allocated_values),
        "reserved_gb": sum(reserved_values) / len(reserved_values),
        "max_allocated_gb": torch.cuda.max_memory_allocated() / 1024**3,
        "total_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3
    }

def create_test_csv(size: str = "medium") -> str:
    """
    创建测试CSV文件
    
    Args:
        size: 文件大小 (small, medium, large)
        
    Returns:
        临时文件路径
    """
    sizes = {
        "small": 100,
        "medium": 500,
        "large": 1000
    }
    
    rows = sizes.get(size, 500)
    
    # 创建测试数据
    data = []
    for i in range(rows):
        data.append({
            "id": f"ID_{i:04d}",
            "name": f"测试用户_{i}",
            "description": f"这是第{i}条测试数据，用于验证向量化服务的显存管理功能。" * 3,
            "category": f"类别_{i % 10}",
            "risk_level": ["低", "中", "高"][i % 3],
            "content": f"详细内容描述：{i} " * 20  # 增加文本长度
        })
    
    df = pd.DataFrame(data)
    
    # 保存为临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    df.to_csv(temp_file.name, index=False, encoding='utf-8')
    temp_file.close()
    
    return temp_file.name

def upload_and_vectorize(file_path: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    上传文件并进行向量化
    
    Args:
        file_path: 文件路径
        base_url: 服务地址
        
    Returns:
        响应结果
    """
    try:
        # 上传文件
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload", files=files, timeout=120)
        
        if response.status_code != 200:
            return {"error": f"上传失败: {response.status_code}, {response.text}"}
        
        result = response.json()
        file_code = result.get("file_code")
        
        if not file_code:
            return {"error": "未获得file_code"}
        
        # 等待向量化完成
        max_wait = 120  # 最多等待2分钟
        wait_time = 0
        
        while wait_time < max_wait:
            status_response = requests.get(f"{base_url}/status/{file_code}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                if status_data.get("status") == "completed":
                    return {
                        "success": True,
                        "file_code": file_code,
                        "document_count": status_data.get("document_count", 0)
                    }
                elif status_data.get("status") == "failed":
                    return {"error": f"向量化失败: {status_data}"}
            
            time.sleep(2)
            wait_time += 2
        
        return {"error": "向量化超时"}
        
    except Exception as e:
        return {"error": f"请求异常: {e}"}

def get_memory_status(base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    获取服务内存状态
    
    Args:
        base_url: 服务地址
        
    Returns:
        内存状态
    """
    try:
        response = requests.get(f"{base_url}/status/memory", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"状态查询失败: {response.status_code}"}
    except Exception as e:
        return {"error": f"状态查询异常: {e}"}

def cleanup_memory(base_url: str = "http://localhost:8000", force: bool = False) -> Dict[str, Any]:
    """
    手动清理内存
    
    Args:
        base_url: 服务地址
        force: 是否强制清理
        
    Returns:
        清理结果
    """
    try:
        response = requests.post(f"{base_url}/status/memory/cleanup", 
                               json={"force": force}, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"清理失败: {response.status_code}"}
    except Exception as e:
        return {"error": f"清理异常: {e}"}

def run_memory_leak_test(rounds: int = 5, base_url: str = "http://localhost:8000"):
    """
    运行显存泄漏测试
    
    Args:
        rounds: 测试轮数
        base_url: 服务地址
    """
    print("🧪 开始精确GPU显存泄漏测试")
    print("=" * 60)
    
    # 检查CUDA可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU显存测试")
        return
    
    print(f"🔍 GPU设备: {torch.cuda.get_device_name()}")
    print(f"📊 总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f}GB")
    print()
    
    # 记录测试结果
    results = []
    
    # 基准测试
    print("📏 获取基准显存使用...")
    baseline_local = precise_memory_check()
    baseline_service = get_memory_status(base_url)
    
    print(f"   本地监控: {baseline_local.get('allocated_gb', 0):.3f}GB")
    if 'error' not in baseline_service:
        service_memory = baseline_service.get('memory_info', {})
        print(f"   服务监控: {service_memory.get('allocated', 0):.3f}GB")
    
    print()
    
    # 多轮测试
    for round_num in range(1, rounds + 1):
        print(f"🔄 第 {round_num}/{rounds} 轮测试")
        
        # 创建测试文件
        test_file = create_test_csv("medium")
        print(f"   📄 创建测试文件: {Path(test_file).name}")
        
        # 记录开始时的显存
        memory_before_local = precise_memory_check()
        memory_before_service = get_memory_status(base_url)
        
        print(f"   📊 向量化前显存: {memory_before_local.get('allocated_gb', 0):.3f}GB")
        
        # 上传并向量化
        print("   🚀 开始向量化...")
        result = upload_and_vectorize(test_file, base_url)
        
        if 'error' in result:
            print(f"   ❌ 向量化失败: {result['error']}")
            os.unlink(test_file)
            continue
        
        print(f"   ✅ 向量化完成: {result.get('document_count', 0)} 个文档")
        
        # 等待一段时间确保清理完成
        print("   ⏳ 等待清理完成...")
        time.sleep(3)
        
        # 记录结束时的显存
        memory_after_local = precise_memory_check()
        memory_after_service = get_memory_status(base_url)
        
        # 计算显存变化
        local_change = memory_after_local.get('allocated_gb', 0) - memory_before_local.get('allocated_gb', 0)
        
        service_change = 0
        if ('error' not in memory_before_service and 'error' not in memory_after_service):
            before_allocated = memory_before_service.get('memory_info', {}).get('allocated', 0)
            after_allocated = memory_after_service.get('memory_info', {}).get('allocated', 0)
            service_change = after_allocated - before_allocated
        
        print(f"   📊 向量化后显存: {memory_after_local.get('allocated_gb', 0):.3f}GB")
        print(f"   📈 显存变化量: {local_change:.3f}GB (本地) / {service_change:.3f}GB (服务)")
        
        # 记录结果
        result_data = {
            "round": round_num,
            "document_count": result.get('document_count', 0),
            "memory_before": memory_before_local.get('allocated_gb', 0),
            "memory_after": memory_after_local.get('allocated_gb', 0),
            "memory_change": local_change,
            "service_memory_change": service_change
        }
        results.append(result_data)
        
        # 清理测试文件
        os.unlink(test_file)
        
        # 判断是否有泄漏
        if abs(local_change) > 0.01:  # 10MB阈值
            print(f"   ⚠️  检测到显存变化: {local_change:.3f}GB")
        else:
            print("   ✅ 显存使用稳定")
        
        print()
    
    # 分析结果
    print("📊 测试结果分析")
    print("=" * 60)
    
    total_change = sum(r['memory_change'] for r in results)
    avg_change = total_change / len(results) if results else 0
    max_change = max((abs(r['memory_change']) for r in results), default=0)
    
    print(f"📈 总显存变化: {total_change:.3f}GB")
    print(f"📊 平均每轮变化: {avg_change:.3f}GB")
    print(f"📏 最大单轮变化: {max_change:.3f}GB")
    
    # 泄漏判断
    if total_change > 0.05:  # 50MB总阈值
        print("❌ 检测到显存泄漏！")
        print("💡 建议:")
        print("   1. 检查torch.cuda.synchronize()是否正确调用")
        print("   2. 验证torch.no_grad()是否正确使用")
        print("   3. 确认模型内部缓存清理")
    elif max_change > 0.02:  # 20MB单轮阈值
        print("⚠️  显存使用不够稳定")
        print("💡 可能需要优化清理时机")
    else:
        print("✅ 显存管理正常，无泄漏检测")
    
    print()
    
    # 详细结果
    print("📋 详细测试结果:")
    print("-" * 80)
    print(f"{'轮次':<6} {'文档数':<8} {'前显存':<10} {'后显存':<10} {'变化量':<10} {'状态'}")
    print("-" * 80)
    
    for r in results:
        status = "正常" if abs(r['memory_change']) <= 0.01 else "异常"
        print(f"{r['round']:<6} {r['document_count']:<8} "
              f"{r['memory_before']:.3f}GB{'':<3} {r['memory_after']:.3f}GB{'':<3} "
              f"{r['memory_change']:+.3f}GB{'':<3} {status}")

def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="精确GPU显存泄漏测试")
    parser.add_argument("--rounds", type=int, default=5, help="测试轮数")
    parser.add_argument("--url", default="http://localhost:8000", help="服务地址")
    parser.add_argument("--cleanup", action="store_true", help="测试前强制清理")
    
    args = parser.parse_args()
    
    if args.cleanup:
        print("🧹 执行测试前清理...")
        cleanup_result = cleanup_memory(args.url, force=True)
        if 'error' in cleanup_result:
            print(f"   ⚠️  清理失败: {cleanup_result['error']}")
        else:
            print("   ✅ 清理完成")
        print()
    
    run_memory_leak_test(args.rounds, args.url)

if __name__ == "__main__":
    main() 