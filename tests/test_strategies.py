#!/usr/bin/env python3
"""
风险预测策略测试脚本
演示智能选择策略和多批次分析策略的使用
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.enhanced_risk_prediction_api import (
    EnhancedRiskPredictionAPI,
    AnalysisStrategy,
    quick_predict,
    compare_all_strategies,
)
import json
from datetime import datetime


def print_separator(title: str):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f" {title} ")
    print("=" * 60)


def print_result(result: dict, title: str):
    """格式化打印结果"""
    print_separator(title)

    if result.get("success"):
        print(f"✅ 分析成功")
        print(f"文件编码: {result.get('file_code', 'N/A')}")
        print(
            f"分析策略: {result.get('analysis_strategy', result.get('analysis_method', 'N/A'))}"
        )
        print(f"违约概率: {result.get('default_probability', 'N/A'):.2%}")
        print(f"风险等级: {result.get('risk_level', 'N/A')}")
        print(f"风险评分: {result.get('risk_score', 'N/A')}")

        # 显示文档使用情况
        if "documents_analyzed" in result:
            print(f"分析文档数: {result['documents_analyzed']}")
        elif "total_batches" in result:
            print(f"分析批次数: {result['total_batches']}")

        # 显示策略描述
        if "strategy_description" in result:
            print(f"策略说明: {result['strategy_description']}")

    else:
        print(f"❌ 分析失败: {result.get('error', '未知错误')}")


def run_smart_selection_test():
    """测试智能选择策略"""
    print_separator("测试智能选择策略")

    # 获取可用文件
    api = EnhancedRiskPredictionAPI()
    files = api.list_available_files()

    if not files:
        print("❌ 没有可用的文件进行测试")
        return None

    file_code = files[0]["file_code"]
    print(f"使用文件编码: {file_code}")

    # 使用智能选择策略
    result = api.predict_by_file_code(file_code, AnalysisStrategy.SMART_SELECTION)
    print_result(result, "智能选择策略结果")

    return result


def run_multi_batch_test():
    """测试多批次分析策略"""
    print_separator("测试多批次分析策略")

    # 获取可用文件
    api = EnhancedRiskPredictionAPI()
    files = api.list_available_files()

    if not files:
        print("❌ 没有可用的文件进行测试")
        return None

    file_code = files[0]["file_code"]
    print(f"使用文件编码: {file_code}")

    # 使用多批次策略
    result = api.predict_by_file_code(file_code, AnalysisStrategy.MULTI_BATCH)
    print_result(result, "多批次分析策略结果")

    return result


def run_strategy_comparison_test():
    """测试策略比较"""
    print_separator("策略比较分析")

    # 获取可用文件
    api = EnhancedRiskPredictionAPI()
    files = api.list_available_files()

    if not files:
        print("❌ 没有可用的文件进行测试")
        return

    file_code = files[0]["file_code"]
    print(f"使用文件编码: {file_code}")

    # 比较策略
    comparison_result = api.compare_strategies(file_code)

    if comparison_result.get("success"):
        print("✅ 策略比较完成")

        # 显示比较结果
        comparison = comparison_result.get("comparison", {})

        # 风险评估比较
        risk_comp = comparison.get("risk_assessment_comparison", {})
        if risk_comp:
            print("\n📊 风险评估比较:")
            prob_comp = risk_comp.get("default_probability", {})
            print(
                f"  智能选择策略违约概率: {prob_comp.get('smart_selection', 'N/A'):.2%}"
            )
            print(f"  多批次策略违约概率: {prob_comp.get('multi_batch', 'N/A'):.2%}")
            print(f"  概率差异: {prob_comp.get('difference', 'N/A'):.2%}")
            print(f"  一致性: {prob_comp.get('consistency', 'N/A')}")

            level_comp = risk_comp.get("risk_level", {})
            print(f"  智能选择策略风险等级: {level_comp.get('smart_selection', 'N/A')}")
            print(f"  多批次策略风险等级: {level_comp.get('multi_batch', 'N/A')}")
            print(f"  等级一致: {'是' if level_comp.get('consistent') else '否'}")

        # 策略特点比较
        strategy_comp = comparison.get("strategy_comparison", {})
        if strategy_comp:
            print("\n🔍 策略特点比较:")
            smart_info = strategy_comp.get("smart_selection", {})
            batch_info = strategy_comp.get("multi_batch", {})

            print(f"  智能选择策略:")
            print(f"    - 使用文档: {smart_info.get('documents_used', 'N/A')}")
            print(f"    - 分析速度: {smart_info.get('analysis_speed', 'N/A')}")
            print(f"    - 数据覆盖: {smart_info.get('data_coverage', 'N/A')}")
            print(f"    - 适用场景: {smart_info.get('suitable_for', 'N/A')}")

            print(f"  多批次策略:")
            print(f"    - 使用文档: {batch_info.get('documents_used', 'N/A')}")
            print(f"    - 分析速度: {batch_info.get('analysis_speed', 'N/A')}")
            print(f"    - 数据覆盖: {batch_info.get('data_coverage', 'N/A')}")
            print(f"    - 适用场景: {batch_info.get('suitable_for', 'N/A')}")

        # 建议
        recommendations = comparison.get("recommendations", [])
        if recommendations:
            print("\n💡 建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
    else:
        print(f"❌ 策略比较失败: {comparison_result.get('error', '未知错误')}")


def run_quick_functions_test():
    """测试快捷函数"""
    print_separator("测试快捷函数")

    # 获取可用文件
    api = EnhancedRiskPredictionAPI()
    files = api.list_available_files()

    if not files:
        print("❌ 没有可用的文件进行测试")
        return

    file_code = files[0]["file_code"]
    print(f"使用文件编码: {file_code}")

    # 测试快速预测函数
    print("\n🚀 快速智能选择预测:")
    smart_result = quick_predict(file_code, "smart")
    if smart_result.get("success"):
        print(f"  违约概率: {smart_result.get('default_probability', 'N/A'):.2%}")
        print(f"  风险等级: {smart_result.get('risk_level', 'N/A')}")
    else:
        print(f"  失败: {smart_result.get('error', '未知错误')}")

    print("\n🔄 快速多批次预测:")
    batch_result = quick_predict(file_code, "batch")
    if batch_result.get("success"):
        print(f"  违约概率: {batch_result.get('default_probability', 'N/A'):.2%}")
        print(f"  风险等级: {batch_result.get('risk_level', 'N/A')}")
        print(f"  分析批次: {batch_result.get('total_batches', 'N/A')}")
    else:
        print(f"  失败: {batch_result.get('error', '未知错误')}")


def main():
    """主函数"""
    print_separator("风险预测策略测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 1. 测试智能选择策略
        smart_result = run_smart_selection_test()

        # 2. 测试多批次分析策略
        batch_result = run_multi_batch_test()

        # 3. 测试策略比较
        run_strategy_comparison_test()

        # 4. 测试快捷函数
        run_quick_functions_test()

        print_separator("测试完成")
        print("✅ 所有测试已完成")

        # 总结
        if smart_result and batch_result:
            if smart_result.get("success") and batch_result.get("success"):
                smart_prob = smart_result.get("default_probability", 0)
                batch_prob = batch_result.get("default_probability", 0)
                print(f"\n📋 测试总结:")
                print(f"  智能选择策略违约概率: {smart_prob:.2%}")
                print(f"  多批次策略违约概率: {batch_prob:.2%}")
                print(f"  概率差异: {abs(smart_prob - batch_prob):.2%}")

                if abs(smart_prob - batch_prob) < 0.05:
                    print("  🎯 两种策略结果高度一致")
                elif abs(smart_prob - batch_prob) < 0.15:
                    print("  ✅ 两种策略结果基本一致")
                else:
                    print("  ⚠️ 两种策略结果存在差异，建议进一步分析")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()