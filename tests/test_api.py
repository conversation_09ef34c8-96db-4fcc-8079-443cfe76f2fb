#!/usr/bin/env python3
"""
Deep Risk RAG API测试脚本
测试新的异步架构API功能
"""

import sys
import time
import json
import requests
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import setup_logging, get_logger

# 设置日志
setup_logging(level="INFO", service_name="api-test")
logger = get_logger(__name__)


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化API测试器
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30
        
        logger.info(f"API测试器初始化: {base_url}")
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        try:
            logger.info("测试健康检查接口...")
            
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 健康检查通过: {data.get('status')}")
                
                # 检查各组件状态
                checks = data.get('checks', {})
                for component, status in checks.items():
                    status_icon = "✅" if status else "❌"
                    logger.info(f"  {status_icon} {component}: {'健康' if status else '异常'}")
                
                return data.get('status') == 'healthy'
            else:
                logger.error(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 健康检查异常: {e}")
            return False
    
    def test_file_upload(self, file_path: str) -> Optional[str]:
        """
        测试文件上传接口
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            文件编码，如果失败返回None
        """
        try:
            logger.info(f"测试文件上传: {file_path}")
            
            if not Path(file_path).exists():
                logger.error(f"❌ 测试文件不存在: {file_path}")
                return None
            
            with open(file_path, 'rb') as f:
                files = {'file': (Path(file_path).name, f, 'application/octet-stream')}
                data = {
                    'force_reprocess': False,
                    'chunk_size': 1000,
                    'chunk_overlap': 200
                }
                
                response = self.session.post(
                    f"{self.base_url}/upload/",
                    files=files,
                    data=data
                )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    file_code = result.get('file_code')
                    task_id = result.get('task_id')
                    logger.info(f"✅ 文件上传成功: {file_code}")
                    logger.info(f"  任务ID: {task_id}")
                    return file_code
                else:
                    logger.error(f"❌ 文件上传失败: {result.get('message')}")
                    return None
            else:
                logger.error(f"❌ 文件上传失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 文件上传异常: {e}")
            return None
    
    def test_file_status(self, file_code: str) -> bool:
        """
        测试文件状态查询
        
        Args:
            file_code: 文件编码
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"测试文件状态查询: {file_code}")
            
            response = self.session.get(f"{self.base_url}/status/{file_code}")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                logger.info(f"✅ 文件状态: {status}")
                return True
            else:
                logger.error(f"❌ 状态查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 状态查询异常: {e}")
            return False
    
    def test_analysis(self, file_code: str) -> Optional[str]:
        """
        测试风险分析接口
        
        Args:
            file_code: 文件编码
            
        Returns:
            分析ID，如果失败返回None
        """
        try:
            logger.info(f"测试风险分析: {file_code}")
            
            data = {
                'analysis_type': 'default',
                'options': {},
                'priority': 1
            }
            
            response = self.session.post(
                f"{self.base_url}/analyze/{file_code}",
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_id = result.get('analysis_id')
                    task_id = result.get('task_id')
                    logger.info(f"✅ 分析任务提交成功: {analysis_id}")
                    logger.info(f"  任务ID: {task_id}")
                    return analysis_id
                else:
                    logger.error(f"❌ 分析提交失败: {result.get('message')}")
                    return None
            else:
                logger.error(f"❌ 分析提交失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 分析提交异常: {e}")
            return None
    
    def test_task_status(self, task_id: str) -> bool:
        """
        测试任务状态查询
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"测试任务状态查询: {task_id}")
            
            response = self.session.get(f"{self.base_url}/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                progress = result.get('progress', {})
                logger.info(f"✅ 任务状态: {status}")
                if progress:
                    logger.info(f"  进度: {progress}")
                return True
            else:
                logger.error(f"❌ 任务状态查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 任务状态查询异常: {e}")
            return False
    
    def test_analysis_result(self, analysis_id: str) -> bool:
        """
        测试分析结果查询
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"测试分析结果查询: {analysis_id}")
            
            response = self.session.get(f"{self.base_url}/result/{analysis_id}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_result = result.get('analysis_result')
                    if analysis_result:
                        logger.info(f"✅ 分析结果获取成功")
                        logger.info(f"  违约概率: {analysis_result.get('default_probability', 'N/A')}")
                        logger.info(f"  风险等级: {analysis_result.get('risk_level', 'N/A')}")
                    else:
                        logger.info("✅ 分析结果查询成功，但结果为空（可能还在处理中）")
                    return True
                else:
                    logger.warning(f"⚠️  分析结果: {result.get('message')}")
                    return True  # 这可能是正常的（任务还在进行中）
            else:
                logger.error(f"❌ 分析结果查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 分析结果查询异常: {e}")
            return False
    
    def test_files_list(self) -> bool:
        """测试文件列表接口"""
        try:
            logger.info("测试文件列表查询...")
            
            response = self.session.get(f"{self.base_url}/files/")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    files = result.get('files', [])
                    total = result.get('total', 0)
                    logger.info(f"✅ 文件列表查询成功: {total} 个文件")
                    return True
                else:
                    logger.error(f"❌ 文件列表查询失败: {result.get('message')}")
                    return False
            else:
                logger.error(f"❌ 文件列表查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 文件列表查询异常: {e}")
            return False
    
    def wait_for_task_completion(self, task_id: str, timeout: int = 300) -> bool:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            是否成功完成
        """
        logger.info(f"等待任务完成: {task_id} (超时: {timeout}秒)")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{self.base_url}/tasks/{task_id}")
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get('status')
                    
                    if status == 'SUCCESS':
                        logger.info(f"✅ 任务完成: {task_id}")
                        return True
                    elif status == 'FAILURE':
                        error = result.get('error', 'Unknown error')
                        logger.error(f"❌ 任务失败: {task_id} - {error}")
                        return False
                    elif status in ['PENDING', 'STARTED', 'PROGRESS']:
                        progress = result.get('progress', {})
                        stage = progress.get('stage', 'Unknown')
                        percent = progress.get('progress', 0)
                        logger.info(f"⏳ 任务进行中: {stage} ({percent}%)")
                    else:
                        logger.warning(f"⚠️  未知任务状态: {status}")
                
                time.sleep(5)  # 等待5秒后重试
                
            except Exception as e:
                logger.error(f"❌ 查询任务状态异常: {e}")
                time.sleep(5)
        
        logger.error(f"❌ 任务超时: {task_id}")
        return False
    
    def run_full_test(self, test_file: str = None) -> bool:
        """
        运行完整测试流程
        
        Args:
            test_file: 测试文件路径
            
        Returns:
            是否全部通过
        """
        logger.info("🚀 开始完整API测试流程")
        
        # 1. 健康检查
        if not self.test_health_check():
            logger.error("❌ 健康检查失败，停止测试")
            return False
        
        # 2. 文件列表查询
        if not self.test_files_list():
            logger.error("❌ 文件列表查询失败")
            return False
        
        # 如果没有提供测试文件，只进行基础测试
        if not test_file:
            logger.info("✅ 基础API测试完成")
            return True
        
        # 3. 文件上传
        file_code = self.test_file_upload(test_file)
        if not file_code:
            logger.error("❌ 文件上传失败，停止测试")
            return False
        
        # 4. 文件状态查询
        if not self.test_file_status(file_code):
            logger.error("❌ 文件状态查询失败")
            return False
        
        # 5. 等待向量化完成（简单等待）
        logger.info("⏳ 等待向量化完成...")
        time.sleep(10)
        
        # 6. 风险分析
        analysis_id = self.test_analysis(file_code)
        if not analysis_id:
            logger.error("❌ 风险分析提交失败，停止测试")
            return False
        
        # 7. 任务状态查询
        if not self.test_task_status(analysis_id):
            logger.error("❌ 任务状态查询失败")
            return False
        
        # 8. 等待分析完成
        if not self.wait_for_task_completion(analysis_id, timeout=180):
            logger.warning("⚠️  分析任务未在预期时间内完成")
        
        # 9. 分析结果查询
        if not self.test_analysis_result(analysis_id):
            logger.error("❌ 分析结果查询失败")
            return False
        
        logger.info("🎉 完整API测试流程完成")
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deep Risk RAG API测试脚本")
    parser.add_argument("--url", default="http://localhost:8000", help="API基础URL")
    parser.add_argument("--file", help="测试文件路径")
    parser.add_argument("--basic", action="store_true", help="只进行基础测试")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = APITester(args.url)
    
    # 运行测试
    if args.basic:
        success = tester.run_full_test()
    else:
        success = tester.run_full_test(args.file)
    
    if success:
        logger.info("🎉 所有测试通过")
        sys.exit(0)
    else:
        logger.error("❌ 部分测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
