# 模型版本记录
# 记录项目中使用的模型版本和相关信息

## BGE-M3 嵌入模型
模型名称: BAAI/bge-m3
Hugging Face 地址: https://huggingface.co/BAAI/bge-m3
本地路径: ./models/bge-m3-safetensors-only/
下载日期: 2024-06-24
文件大小: ~4.3GB
版本: 主分支（latest）
用途: 多语言文本嵌入，支持密集、稀疏、多向量检索

主要文件:
- model.safetensors (2.1GB) - 主模型权重
- tokenizer.json (16MB) - 分词器
- config.json (687B) - 模型配置
- colbert_linear.pt (2.0MB) - ColBERT线性层
- sparse_linear.pt (3.4KB) - 稀疏检索线性层

## 下载命令
```bash
python models/download_models.py
```

## 更新历史
- 2024-06-24: 初始下载BGE-M3模型
- 注: BGE-M3为稳定版本，预期很少更新

## 验证
模型文件校验可通过以下方式验证:
```python
from src.embeddings import BGEM3Embeddings
embeddings = BGEM3Embeddings()
print(embeddings.get_model_info())
``` 