# 模型文件管理指南

## 📁 目录结构

```
models/
├── README.md                    # 本文档
├── download_models.py           # 模型下载脚本
├── model_versions.txt           # 模型版本记录
└── bge-m3-safetensors-only/     # BGE-M3 模型文件（不提交到Git）
    ├── model.safetensors        # 2.1GB - 主模型文件
    ├── tokenizer.json           # 16MB - 分词器
    ├── config.json              # 配置文件
    └── ...                      # 其他模型组件
```

## 🚫 Git 管理原则

### 不提交到Git的文件：
- ✅ **模型权重文件** (*.safetensors, *.bin, *.pt)
- ✅ **大型tokenizer文件** (tokenizer.json > 1MB)
- ✅ **模型数据文件** (*.onnx, *.pb, *.h5)

### 提交到Git的文件：
- ✅ **下载脚本** (download_models.py)
- ✅ **配置文件** (小于1MB的json/yaml文件)
- ✅ **文档文件** (README.md)
- ✅ **版本记录** (model_versions.txt)

## 🔄 模型获取流程

### 1. 首次设置
```bash
# 克隆仓库
git clone <your-repo-url>
cd deep-risk-rag

# 下载模型
python models/download_models.py
```

### 2. 团队协作
```bash
# 其他开发者只需运行
python models/download_models.py
```

### 3. 模型更新
```bash
# 更新模型时，只需修改下载脚本
# 然后提交脚本变更，不提交模型文件
git add models/download_models.py models/model_versions.txt
git commit -m "更新BGE-M3模型到v1.5"
```

## 📝 模型版本管理

我们通过以下文件跟踪模型版本：
- `model_versions.txt` - 记录当前使用的模型版本
- `download_models.py` - 包含具体的下载逻辑

## 🛠️ 故障排除

### 模型文件缺失
```bash
# 重新下载模型
python models/download_models.py

# 或者手动下载特定模型
python -c "
from huggingface_hub import snapshot_download
snapshot_download('BAAI/bge-m3', local_dir='./models/bge-m3-safetensors-only')
"
```

### 磁盘空间不足
```bash
# 清理不必要的模型文件
rm -rf models/bge-m3-safetensors-only/.cache/
rm -rf models/bge-m3-safetensors-only/onnx/  # 如果不使用ONNX推理
```

## 🔒 .gitignore 配置

确保 `.gitignore` 包含以下规则：
```gitignore
# AI/ML specific
models/bge-m3-safetensors-only/
models/*.safetensors
models/*.bin
models/*.pt
*.pkl
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
```

## 💡 高级选项

### 使用Git LFS（可选）
如果确实需要版本控制模型文件：
```bash
# 安装Git LFS
git lfs install

# 配置LFS追踪大文件
git lfs track "*.safetensors"
git lfs track "*.bin"

# 然后正常提交
git add .gitattributes
git commit -m "配置Git LFS"
```

### 使用DVC（可选）
对于数据科学项目，可以考虑使用DVC管理模型文件：
```bash
pip install dvc
dvc add models/bge-m3-safetensors-only
git add models/bge-m3-safetensors-only.dvc .gitignore
```

## 📊 当前模型信息

- **模型名称**: BAAI/bge-m3
- **模型大小**: ~4.3GB
- **用途**: 多语言文本嵌入
- **更新频率**: 稳定版本，很少更新 