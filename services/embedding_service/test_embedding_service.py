#!/usr/bin/env python3
"""
BGE-M3嵌入服务测试脚本
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import setup_logging, get_logger
from services.embedding_service.config import config
from services.embedding_service.core.embedding_manager import EmbeddingManager

# 设置日志
setup_logging(level="INFO", service_name="embedding-test")
logger = get_logger(__name__)


async def test_embedding_manager():
    """测试嵌入管理器"""
    logger.info("开始测试嵌入管理器...")

    try:
        # 创建嵌入管理器
        manager = EmbeddingManager()

        # 初始化
        logger.info("初始化嵌入管理器...")
        await manager.initialize()

        # 检查就绪状态
        is_ready = await manager.is_ready()
        logger.info(f"嵌入管理器就绪状态: {is_ready}")

        if not is_ready:
            logger.error("嵌入管理器未就绪，测试终止")
            return False

        # 测试单个查询嵌入
        logger.info("测试单个查询嵌入...")
        test_query = "这是一个测试查询"
        query_embedding = await manager.embed_query(test_query)
        logger.info(f"查询嵌入维度: {len(query_embedding)}")
        logger.info(f"查询嵌入前5个值: {query_embedding[:5]}")

        # 测试批量文档嵌入
        logger.info("测试批量文档嵌入...")
        test_documents = [
            "这是第一个测试文档",
            "这是第二个测试文档",
            "这是第三个测试文档",
        ]
        doc_embeddings = await manager.embed_documents(test_documents)
        logger.info(f"文档嵌入数量: {len(doc_embeddings)}")
        logger.info(f"每个嵌入维度: {len(doc_embeddings[0]) if doc_embeddings else 0}")

        # 获取模型信息
        logger.info("获取模型信息...")
        model_info = await manager.get_model_info()
        logger.info(f"模型信息: {model_info}")

        # 清理
        logger.info("清理资源...")
        await manager.cleanup()

        logger.info("✅ 嵌入管理器测试完成")
        return True

    except Exception as e:
        logger.error(f"❌ 嵌入管理器测试失败: {e}")
        return False


async def test_api_server():
    """测试API服务器"""
    logger.info("开始测试API服务器...")

    try:
        import uvicorn
        from services.embedding_service.main import app

        # 这里只是验证应用可以创建，不实际启动服务器
        logger.info(f"API应用创建成功: {app.title}")
        logger.info("✅ API服务器测试完成")
        return True

    except Exception as e:
        logger.error(f"❌ API服务器测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("🧪 BGE-M3嵌入服务测试开始")
    logger.info("=" * 60)

    # 显示配置信息
    logger.info(f"模型路径: {config.model_name}")
    logger.info(f"设备: {config.device}")
    logger.info(f"批次大小: {config.batch_size}")
    logger.info(f"最大长度: {config.max_length}")

    success_count = 0
    total_tests = 2

    # 测试嵌入管理器
    if await test_embedding_manager():
        success_count += 1

    # 测试API服务器
    if await test_api_server():
        success_count += 1

    # 总结
    logger.info("=" * 60)
    logger.info(f"🧪 测试完成: {success_count}/{total_tests} 通过")

    if success_count == total_tests:
        logger.info("✅ 所有测试通过")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
