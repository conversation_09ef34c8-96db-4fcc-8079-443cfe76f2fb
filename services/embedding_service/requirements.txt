# BGE-M3嵌入服务独立依赖
# 确保服务可以完全独立部署和运行

# 核心框架
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# BGE-M3模型和机器学习
FlagEmbedding>=1.2.10
torch>=2.0.0
transformers>=4.36.0
sentence-transformers>=2.2.2

# 数值计算
numpy>=1.24.0
scikit-learn>=1.3.0

# 系统监控和性能
psutil>=5.9.0

# 日志和配置
python-dotenv>=1.0.0

# HTTP客户端和网络
httpx>=0.25.0

# 缓存支持
cachetools>=5.3.0

# 数据处理
pandas>=2.0.0

# 安全和认证（可选）
# python-jose[cryptography]>=3.3.0
# passlib[bcrypt]>=1.7.4

# 监控和指标（可选）
# prometheus-client>=0.17.0

# 开发和测试工具（开发环境）
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
