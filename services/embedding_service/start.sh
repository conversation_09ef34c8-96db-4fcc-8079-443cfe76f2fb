#!/bin/bash

# BGE-M3 嵌入服务独立启动脚本
# 支持多种部署模式

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "BGE-M3 嵌入服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -d, --dev         开发模式启动"
    echo "  -p, --prod        生产模式启动"
    echo "  --docker          Docker模式启动"
    echo "  --check           检查环境和依赖"
    echo "  --install         安装依赖"
    echo ""
    echo "环境变量:"
    echo "  HOST              服务主机地址 (默认: 0.0.0.0)"
    echo "  PORT              服务端口 (默认: 8004)"
    echo "  LOG_LEVEL         日志级别 (默认: INFO)"
    echo "  DEVICE            设备类型 (auto/cpu/cuda/mps)"
    echo "  MODEL_NAME        模型路径"
    echo ""
}

# 检查Python环境
check_python() {
    print_header "🐍 检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_message "Python版本: $python_version"
    
    # 检查Python版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
        print_error "需要Python 3.10或更高版本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_header "📦 检查依赖..."
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 检查关键依赖
    python3 -c "
import sys
try:
    import torch
    import transformers
    import FlagEmbedding
    import fastapi
    print('✅ 关键依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少依赖: {e}')
    sys.exit(1)
"
}

# 检查模型文件
check_models() {
    print_header "🤖 检查模型文件..."
    
    model_path=${MODEL_NAME:-"../../models/bge-m3-safetensors-only"}
    
    if [ ! -d "$model_path" ]; then
        print_warning "模型目录不存在: $model_path"
        print_message "将在首次运行时自动下载模型"
    else
        print_message "模型目录存在: $model_path"
    fi
}

# 检查GPU支持
check_gpu() {
    print_header "🎮 检查GPU支持..."
    
    python3 -c "
import torch
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA设备数量: {torch.cuda.device_count()}')
    print(f'当前设备: {torch.cuda.current_device()}')
    print(f'设备名称: {torch.cuda.get_device_name()}')

if hasattr(torch.backends, 'mps'):
    print(f'MPS可用: {torch.backends.mps.is_available()}')
"
}

# 安装依赖
install_dependencies() {
    print_header "📦 安装依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    print_message "安装Python依赖..."
    pip3 install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 设置环境变量
setup_environment() {
    print_header "⚙️ 设置环境..."
    
    # 设置默认环境变量
    export HOST=${HOST:-"0.0.0.0"}
    export PORT=${PORT:-"8004"}
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/../.."
    
    print_message "服务地址: $HOST:$PORT"
    print_message "日志级别: $LOG_LEVEL"
    print_message "Python路径: $PYTHONPATH"
}

# 启动服务
start_service() {
    local mode=$1
    
    print_header "🚀 启动BGE-M3嵌入服务..."
    
    case $mode in
        dev)
            print_message "开发模式启动..."
            export DEBUG=true
            python3 -m uvicorn main:app --host $HOST --port $PORT --reload
            ;;
        prod)
            print_message "生产模式启动..."
            export DEBUG=false
            python3 -m uvicorn main:app --host $HOST --port $PORT --workers 1
            ;;
        docker)
            print_message "Docker模式启动..."
            export DEBUG=false
            exec python3 -m uvicorn main:app --host $HOST --port $PORT
            ;;
        *)
            print_message "标准模式启动..."
            python3 -m uvicorn main:app --host $HOST --port $PORT
            ;;
    esac
}

# 主函数
main() {
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        --check)
            check_python
            check_dependencies
            check_models
            check_gpu
            print_message "环境检查完成"
            ;;
        --install)
            check_python
            install_dependencies
            ;;
        -d|--dev)
            setup_environment
            check_dependencies
            start_service "dev"
            ;;
        -p|--prod)
            setup_environment
            check_dependencies
            start_service "prod"
            ;;
        --docker)
            setup_environment
            start_service "docker"
            ;;
        *)
            setup_environment
            check_dependencies
            start_service "standard"
            ;;
    esac
}

# 执行主函数
main "$@"
