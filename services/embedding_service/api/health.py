"""
健康检查API
"""

import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import APIRouter, Depends, HTTPException, Request
from shared.utils.logger import get_logger
from services.embedding_service.config import config

logger = get_logger(__name__)

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    基础健康检查
    
    Returns:
        健康状态信息
    """
    try:
        return {
            "status": "healthy",
            "service": config.service_name,
            "version": config.service_version,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": "running",
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    详细健康检查
    
    Returns:
        详细的健康状态信息
    """
    try:
        # 导入嵌入管理器
        import services.embedding_service.main as main_module
        embedding_manager = main_module.embedding_manager
        
        # 基础信息
        health_info = {
            "status": "healthy",
            "service": config.service_name,
            "version": config.service_version,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        # 配置信息
        health_info["config"] = {
            "device": config.device,
            "batch_size": config.batch_size,
            "max_length": config.max_length,
            "enable_cache": config.enable_cache,
            "max_concurrent_requests": config.max_concurrent_requests,
        }
        
        # 模型状态
        if embedding_manager:
            model_status = await embedding_manager.get_model_status()
            health_info["model"] = model_status
        else:
            health_info["model"] = {"status": "not_initialized"}
        
        return health_info
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"详细健康检查失败: {str(e)}")


@router.get("/ready")
async def readiness_check(request: Request) -> Dict[str, Any]:
    """
    就绪检查 - 检查服务是否准备好接收请求
    
    Returns:
        就绪状态信息
    """
    try:
        embedding_manager = request.app.state.embedding_manager
        
        if not embedding_manager:
            raise HTTPException(status_code=503, detail="嵌入管理器未初始化")
        
        # 检查模型是否已加载
        is_ready = await embedding_manager.is_ready()
        
        if not is_ready:
            raise HTTPException(status_code=503, detail="模型未准备就绪")
        
        return {
            "status": "ready",
            "service": config.service_name,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "服务已准备就绪"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"就绪检查失败: {e}")
        raise HTTPException(status_code=503, detail=f"就绪检查失败: {str(e)}")


@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """
    存活检查 - 检查服务是否仍在运行
    
    Returns:
        存活状态信息
    """
    return {
        "status": "alive",
        "service": config.service_name,
        "timestamp": datetime.utcnow().isoformat(),
        "message": "服务正在运行"
    }
