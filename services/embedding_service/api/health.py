"""
健康检查API - 支持独立部署
提供完整的健康检查、就绪检查和存活检查
"""

import sys
import psutil
import torch
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# 智能路径设置
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import APIRouter, HTTPException, Request

# 尝试导入共享组件
try:
    from shared.utils.logger import get_logger
    SHARED_AVAILABLE = True
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    SHARED_AVAILABLE = False

from services.embedding_service.config import config

logger = get_logger(__name__)

# 服务启动时间
SERVICE_START_TIME = datetime.utcnow()

logger = get_logger(__name__)

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    基础健康检查
    
    Returns:
        健康状态信息
    """
    try:
        return {
            "status": "healthy",
            "service": config.service_name,
            "version": config.service_version,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": "running",
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/detailed")
async def detailed_health_check(request: Request) -> Dict[str, Any]:
    """
    详细健康检查 - 包含系统资源和模型状态

    Returns:
        详细的健康状态信息
    """
    try:
        # 基础信息
        uptime = datetime.utcnow() - SERVICE_START_TIME

        health_info = {
            "status": "healthy",
            "service": config.service_name,
            "version": config.service_version,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": int(uptime.total_seconds()),
            "uptime_human": str(uptime),
        }

        # 系统资源信息
        try:
            import psutil
            health_info["system"] = {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            }
        except ImportError:
            health_info["system"] = {"status": "psutil_not_available"}

        # GPU信息
        try:
            import torch
            health_info["gpu"] = {
                "cuda_available": torch.cuda.is_available(),
                "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                "mps_available": hasattr(torch.backends, 'mps') and torch.backends.mps.is_available(),
            }

            if torch.cuda.is_available():
                health_info["gpu"]["cuda_memory"] = {
                    "allocated": torch.cuda.memory_allocated(),
                    "cached": torch.cuda.memory_reserved(),
                }
        except ImportError:
            health_info["gpu"] = {"status": "torch_not_available"}

        # 配置信息
        health_info["config"] = {
            "device": config.device,
            "batch_size": config.batch_size,
            "max_length": config.max_length,
            "enable_cache": config.enable_cache,
            "max_concurrent_requests": config.max_concurrent_requests,
            "deployment_mode": config.deployment_mode,
            "environment": config.environment,
        }

        # 模型状态
        try:
            embedding_manager = getattr(request.app.state, 'embedding_manager', None)
            if embedding_manager and hasattr(embedding_manager, 'get_model_status'):
                model_status = await embedding_manager.get_model_status()
                health_info["model"] = model_status
            else:
                health_info["model"] = {"status": "not_initialized"}
        except Exception as e:
            health_info["model"] = {"status": "error", "error": str(e)}

        return health_info

    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"详细健康检查失败: {str(e)}")


@router.get("/ready")
async def readiness_check(request: Request) -> Dict[str, Any]:
    """
    就绪检查 - 检查服务是否准备好接收请求
    
    Returns:
        就绪状态信息
    """
    try:
        embedding_manager = request.app.state.embedding_manager
        
        if not embedding_manager:
            raise HTTPException(status_code=503, detail="嵌入管理器未初始化")
        
        # 检查模型是否已加载
        is_ready = await embedding_manager.is_ready()
        
        if not is_ready:
            raise HTTPException(status_code=503, detail="模型未准备就绪")
        
        return {
            "status": "ready",
            "service": config.service_name,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "服务已准备就绪"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"就绪检查失败: {e}")
        raise HTTPException(status_code=503, detail=f"就绪检查失败: {str(e)}")


@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """
    存活检查 - 检查服务是否仍在运行
    
    Returns:
        存活状态信息
    """
    return {
        "status": "alive",
        "service": config.service_name,
        "timestamp": datetime.utcnow().isoformat(),
        "message": "服务正在运行"
    }
