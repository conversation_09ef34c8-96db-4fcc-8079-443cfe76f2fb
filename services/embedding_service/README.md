# BGE-M3 嵌入服务

独立的BGE-M3文本嵌入计算服务，专门用于Deep Risk RAG系统的向量化需求。

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务
```bash
python main.py
```

服务将在 `http://localhost:8004` 启动。

### 健康检查
```bash
curl http://localhost:8004/api/v1/health/ready
```

## 📋 API 文档

### 嵌入计算

#### 单个查询嵌入
```bash
curl -X POST http://localhost:8004/api/v1/embed/query \
  -H "Content-Type: application/json" \
  -d '{"text": "这是一个测试查询", "normalize": true}'
```

#### 批量文档嵌入
```bash
curl -X POST http://localhost:8004/api/v1/embed/documents \
  -H "Content-Type: application/json" \
  -d '{"texts": ["文档1", "文档2", "文档3"], "normalize": true}'
```

### 服务管理

#### 获取模型信息
```bash
curl http://localhost:8004/api/v1/embed/model/info
```

#### 清理缓存
```bash
curl -X POST http://localhost:8004/api/v1/embed/cache/clear
```

## ⚙️ 配置

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `HOST` | `0.0.0.0` | 服务监听地址 |
| `PORT` | `8004` | 服务端口 |
| `MODEL_NAME` | `./models/bge-m3-safetensors-only` | BGE-M3模型路径 |
| `DEVICE` | `auto` | 计算设备 (cpu/cuda/auto) |
| `BATCH_SIZE` | `32` | 批处理大小 |
| `MAX_LENGTH` | `8192` | 最大序列长度 |
| `ENABLE_CACHE` | `true` | 是否启用缓存 |
| `CACHE_TTL` | `3600` | 缓存过期时间（秒） |
| `MAX_CACHE_SIZE` | `1000` | 最大缓存条目数 |

### 配置文件
创建 `.env` 文件：
```bash
HOST=0.0.0.0
PORT=8004
DEBUG=false
LOG_LEVEL=INFO
MODEL_NAME=/path/to/your/bge-m3-model
DEVICE=auto
BATCH_SIZE=32
MAX_LENGTH=8192
ENABLE_CACHE=true
CACHE_TTL=3600
MAX_CACHE_SIZE=1000
```

## 🧪 测试

### 运行测试
```bash
python test_embedding_service.py
```

### 测试覆盖
- ✅ 嵌入管理器功能测试
- ✅ API服务器创建测试
- ✅ 批量文档嵌入测试
- ✅ 单个查询嵌入测试
- ✅ 模型信息获取测试

## 📊 性能特性

### 优化功能
- **批量处理**: 支持批量嵌入计算，提升吞吐量
- **智能缓存**: LRU + TTL缓存策略，避免重复计算
- **内存管理**: 自动内存清理和监控
- **异步处理**: 支持高并发请求
- **错误恢复**: 完善的错误处理和重试机制

### 性能指标
- **嵌入维度**: 1024
- **最大序列长度**: 8192
- **默认批处理大小**: 32
- **支持并发请求**: 10（可配置）

## 🏗️ 架构设计

```
┌─────────────────────────────────────┐
│         BGE-M3 Embedding Service    │
├─────────────────────────────────────┤
│  FastAPI Application                │
│  ├── Health Check API              │
│  ├── Embedding API                 │
│  └── Model Management API          │
├─────────────────────────────────────┤
│  Embedding Manager                  │
│  ├── BGE Embedding Service         │
│  ├── Cache Manager                 │
│  └── Performance Monitor           │
├─────────────────────────────────────┤
│  BGE-M3 Model                      │
│  ├── Text Encoding                 │
│  ├── Dense Embeddings              │
│  └── Memory Management             │
└─────────────────────────────────────┘
```

## 🔧 开发

### 目录结构
```
services/embedding_service/
├── main.py                    # FastAPI应用入口
├── config.py                  # 配置管理
├── requirements.txt           # 依赖包
├── test_embedding_service.py  # 测试脚本
├── api/
│   ├── health.py             # 健康检查API
│   └── embed.py              # 嵌入计算API
└── core/
    ├── embedding_manager.py   # 嵌入管理器
    ├── bge_embedding.py      # BGE嵌入服务
    └── cache_manager.py      # 缓存管理器
```

### 添加新功能
1. 在 `core/` 目录添加核心逻辑
2. 在 `api/` 目录添加API端点
3. 更新 `main.py` 注册新路由
4. 添加相应的测试

## 📝 日志

### 日志级别
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息（默认）
- `WARNING`: 警告信息
- `ERROR`: 错误信息

### 日志示例
```
2025-07-01 09:46:04,503 - embedding-service - main - INFO - 🚀 启动 embedding-service v1.0.0
2025-07-01 09:46:04,503 - embedding-service - main - INFO - 📡 服务地址: 0.0.0.0:8004
2025-07-01 09:46:04,503 - embedding-service - main - INFO - 🤖 模型路径: /path/to/bge-m3-model
2025-07-01 09:46:04,503 - embedding-service - main - INFO - ✅ 嵌入服务启动完成
```

## 🚨 故障排除

### 常见问题

#### 1. 模型加载失败
```
错误: 无法从本地路径加载 BGE-M3 嵌入模型
解决: 检查模型路径是否正确，确保模型文件完整
```

#### 2. 内存不足
```
错误: CUDA out of memory
解决: 减少 BATCH_SIZE 或使用 CPU 模式
```

#### 3. 端口被占用
```
错误: Address already in use
解决: 更改 PORT 配置或停止占用端口的进程
```

### 调试模式
启用调试模式获取更多信息：
```bash
DEBUG=true python main.py
```

## 📄 许可证

本项目是 Deep Risk RAG 系统的一部分。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**版本**: v1.0.0  
**更新日期**: 2025-07-01
