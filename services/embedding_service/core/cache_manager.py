"""
缓存管理器
用于管理嵌入向量的缓存
"""

import asyncio
import time
from typing import List, Optional, Dict, Any
from collections import OrderedDict
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger

logger = get_logger(__name__)


class CacheManager:
    """内存缓存管理器 - 用于缓存嵌入向量"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存过期时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: OrderedDict = OrderedDict()
        self.access_times: Dict[str, float] = {}
        self.lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expired": 0,
        }
        
        logger.info(f"缓存管理器初始化: 最大大小={max_size}, TTL={ttl}秒")
    
    async def get(self, key: str) -> Optional[List[float]]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的嵌入向量，如果不存在或过期则返回None
        """
        async with self.lock:
            current_time = time.time()
            
            if key not in self.cache:
                self.stats["misses"] += 1
                return None
            
            # 检查是否过期
            access_time = self.access_times.get(key, 0)
            if current_time - access_time > self.ttl:
                # 过期，删除缓存
                del self.cache[key]
                del self.access_times[key]
                self.stats["expired"] += 1
                self.stats["misses"] += 1
                return None
            
            # 更新访问时间并移到末尾（LRU）
            value = self.cache[key]
            self.cache.move_to_end(key)
            self.access_times[key] = current_time
            
            self.stats["hits"] += 1
            return value
    
    async def set(self, key: str, value: List[float]):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 嵌入向量
        """
        async with self.lock:
            current_time = time.time()
            
            # 如果键已存在，更新值和时间
            if key in self.cache:
                self.cache[key] = value
                self.cache.move_to_end(key)
                self.access_times[key] = current_time
                return
            
            # 检查是否需要清理过期项
            await self._cleanup_expired()
            
            # 检查是否需要驱逐最旧的项
            if len(self.cache) >= self.max_size:
                await self._evict_oldest()
            
            # 添加新项
            self.cache[key] = value
            self.access_times[key] = current_time
    
    async def _cleanup_expired(self):
        """清理过期的缓存项"""
        current_time = time.time()
        expired_keys = []
        
        for key, access_time in self.access_times.items():
            if current_time - access_time > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
            del self.access_times[key]
            self.stats["expired"] += 1
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    async def _evict_oldest(self):
        """驱逐最旧的缓存项"""
        if self.cache:
            # OrderedDict的第一个项是最旧的
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
            self.stats["evictions"] += 1
            logger.debug(f"驱逐最旧的缓存项: {oldest_key}")
    
    async def clear(self):
        """清空所有缓存"""
        async with self.lock:
            cleared_count = len(self.cache)
            self.cache.clear()
            self.access_times.clear()
            logger.info(f"清空了 {cleared_count} 个缓存项")
    
    async def size(self) -> int:
        """获取当前缓存大小"""
        return len(self.cache)
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        async with self.lock:
            total_requests = self.stats["hits"] + self.stats["misses"]
            hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0.0
            
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "hits": self.stats["hits"],
                "misses": self.stats["misses"],
                "hit_rate": round(hit_rate, 3),
                "evictions": self.stats["evictions"],
                "expired": self.stats["expired"],
                "total_requests": total_requests,
            }
    
    async def reset_stats(self):
        """重置统计信息"""
        async with self.lock:
            self.stats = {
                "hits": 0,
                "misses": 0,
                "evictions": 0,
                "expired": 0,
            }
            logger.info("缓存统计信息已重置")
    
    async def cleanup_task(self):
        """定期清理任务 - 可以在后台运行"""
        while True:
            try:
                await asyncio.sleep(self.ttl // 4)  # 每1/4 TTL时间清理一次
                async with self.lock:
                    await self._cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"缓存清理任务出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再继续
    
    def __len__(self) -> int:
        """返回缓存大小"""
        return len(self.cache)
    
    def __contains__(self, key: str) -> bool:
        """检查键是否在缓存中"""
        return key in self.cache
