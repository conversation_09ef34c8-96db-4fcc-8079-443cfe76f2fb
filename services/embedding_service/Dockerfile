# BGE-M3 嵌入服务独立 Dockerfile
# 支持GPU和CPU环境的多阶段构建

# 基础镜像
FROM python:3.10-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r embedding && useradd -r -g embedding embedding

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/models /app/cache /app/logs && \
    chown -R embedding:embedding /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV MODEL_CACHE_DIR=/app/cache
ENV LOG_FILE=/app/logs/embedding_service.log

# 切换到非root用户
USER embedding

# 暴露端口
EXPOSE 8004

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8004/api/v1/health/ready || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8004"]
