version: '3.8'

services:
  embedding-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bge-m3-embedding-service
    ports:
      - "${PORT:-8004}:8004"
    environment:
      - HOST=0.0.0.0
      - PORT=8004
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - DEVICE=${DEVICE:-auto}
      - BATCH_SIZE=${BATCH_SIZE:-32}
      - MAX_LENGTH=${MAX_LENGTH:-8192}
      - ENABLE_CACHE=${ENABLE_CACHE:-true}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - DEPLOYMENT_MODE=docker
    volumes:
      # 模型文件挂载
      - ${MODEL_PATH:-../../models}:/app/models:ro
      # 缓存目录
      - embedding_cache:/app/cache
      # 日志目录
      - embedding_logs:/app/logs
    deploy:
      resources:
        # GPU支持 (如果可用)
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        # 资源限制
        limits:
          memory: 8G
          cpus: '4.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/v1/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - embedding-network

  # 可选：Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: embedding-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - embedding-network
    profiles:
      - monitoring

  # 可选：Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: embedding-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - embedding-network
    profiles:
      - monitoring

volumes:
  embedding_cache:
    driver: local
  embedding_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  embedding-network:
    driver: bridge

# 使用说明:
# 1. 基本启动: docker-compose up -d
# 2. 包含监控: docker-compose --profile monitoring up -d
# 3. 仅CPU模式: DEVICE=cpu docker-compose up -d
# 4. 自定义端口: PORT=8005 docker-compose up -d
