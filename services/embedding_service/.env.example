# BGE-M3 嵌入服务环境配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 服务基本配置
# =============================================================================
# 服务名称
SERVICE_NAME=embedding-service

# 服务版本
SERVICE_VERSION=1.0.0

# 服务地址和端口
HOST=0.0.0.0
PORT=8004

# 运行模式
DEBUG=false
LOG_LEVEL=INFO

# 部署环境 (development/staging/production)
ENVIRONMENT=development

# 部署模式 (standalone/docker/k8s)
DEPLOYMENT_MODE=standalone

# =============================================================================
# 模型配置
# =============================================================================
# 模型路径 (相对路径或绝对路径)
MODEL_NAME=../../models/bge-m3-safetensors-only

# 模型缓存目录
MODEL_CACHE_DIR=./models

# 设备配置 (auto/cpu/cuda/mps)
DEVICE=auto

# 强制使用CPU
FORCE_CPU=false

# =============================================================================
# 嵌入配置
# =============================================================================
# 批处理大小
BATCH_SIZE=32

# 最大序列长度
MAX_LENGTH=8192

# 是否标准化嵌入向量
NORMALIZE_EMBEDDINGS=true

# =============================================================================
# 性能配置
# =============================================================================
# 最大并发请求数
MAX_CONCURRENT_REQUESTS=10

# 启用缓存
ENABLE_CACHE=true

# 缓存大小
MAX_CACHE_SIZE=1000

# 缓存TTL (秒)
CACHE_TTL=3600

# 自动清理
AUTO_CLEANUP=true

# 内存监控
ENABLE_MEMORY_MONITORING=true

# =============================================================================
# GPU配置
# =============================================================================
# GPU内存分数 (0.0-1.0)
GPU_MEMORY_FRACTION=0.8

# GPU设备ID
CUDA_DEVICE=0

# =============================================================================
# 安全配置
# =============================================================================
# API密钥 (可选)
# API_KEY=your_api_key_here

# 允许的来源 (CORS)
ALLOWED_ORIGINS=*

# 请求速率限制 (每分钟)
RATE_LIMIT=100

# =============================================================================
# 监控配置
# =============================================================================
# 启用Prometheus指标
ENABLE_METRICS=false

# Prometheus端口
METRICS_PORT=9090

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# 日志配置
# =============================================================================
# 日志格式 (json/text)
LOG_FORMAT=text

# 日志文件路径
LOG_FILE=./logs/embedding_service.log

# 日志轮转大小 (MB)
LOG_MAX_SIZE=100

# 保留日志文件数量
LOG_BACKUP_COUNT=5

# =============================================================================
# Docker特定配置
# =============================================================================
# 容器内模型路径
DOCKER_MODEL_PATH=/app/models

# 容器内缓存路径
DOCKER_CACHE_PATH=/app/cache

# 容器内日志路径
DOCKER_LOG_PATH=/app/logs

# =============================================================================
# 开发配置
# =============================================================================
# 开发模式下的额外配置
DEV_RELOAD=true
DEV_DEBUG_SQL=false
DEV_MOCK_GPU=false
