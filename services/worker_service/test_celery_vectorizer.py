#!/usr/bin/env python3
"""
测试Celery环境中的VectorizationProcessor
验证环境感知功能和健康监测在实际Celery任务中的表现
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from shared.celery_config import celery_app

logger = get_logger(__name__)


def test_health_check_task():
    """测试健康检查任务"""
    logger.info("=" * 60)
    logger.info("测试Celery健康检查任务")
    logger.info("=" * 60)
    
    try:
        # 提交健康检查任务
        logger.info("提交健康检查任务...")
        result = celery_app.send_task('worker.tasks.health_check')
        logger.info(f"任务ID: {result.id}")
        
        # 等待结果
        logger.info("等待任务完成...")
        task_result = result.get(timeout=30)
        
        logger.info("健康检查任务完成!")
        logger.info(f"整体健康状态: {task_result.get('overall_healthy', False)}")
        logger.info(f"总耗时: {task_result.get('total_time', 0):.2f}s")
        
        # 检查向量化组件状态
        if 'vectorization' in task_result:
            vectorization_status = task_result['vectorization']
            logger.info(f"向量化组件状态:")
            logger.info(f"  健康状态: {vectorization_status.get('healthy', False)}")
            logger.info(f"  环境类型: {vectorization_status.get('environment', 'unknown')}")
            logger.info(f"  嵌入服务: {vectorization_status.get('embedding_service', False)}")
            logger.info(f"  ChromaDB: {vectorization_status.get('chromadb', False)}")
        
        logger.info("✅ 健康检查任务测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 健康检查任务测试失败: {e}")
        return False


def test_vectorization_task():
    """测试向量化任务"""
    logger.info("=" * 60)
    logger.info("测试Celery向量化任务")
    logger.info("=" * 60)
    
    try:
        # 创建测试文件
        import tempfile
        import pandas as pd
        
        test_data = {
            'feature1': [1, 2, 3],
            'feature2': ['A', 'B', 'C'],
            'feature3': [0.1, 0.2, 0.3]
        }
        df = pd.DataFrame(test_data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            test_file_path = f.name
        
        logger.info(f"创建测试文件: {test_file_path}")
        
        # 准备任务参数
        file_code = "test_celery_vectorizer"
        from shared.models.file_info import FileInfo, FileStatus
        from datetime import datetime

        file_info = FileInfo(
            file_code=file_code,
            file_name='test.csv',
            file_path=test_file_path,
            file_size=os.path.getsize(test_file_path),
            file_type='.csv',
            status=FileStatus.UPLOADING,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # 提交向量化任务
        logger.info("提交向量化任务...")
        result = celery_app.send_task(
            'worker.tasks.vectorize_file',
            args=[file_code, test_file_path, file_info.model_dump()]
        )
        logger.info(f"任务ID: {result.id}")
        
        # 监控任务进度
        logger.info("监控任务进度...")
        start_time = time.time()
        timeout = 60  # 60秒超时
        
        while not result.ready():
            if time.time() - start_time > timeout:
                logger.error("任务超时")
                return False
            
            # 检查任务状态
            if result.state == 'PROGRESS':
                meta = result.info
                if meta:
                    stage = meta.get('stage', '未知')
                    progress = meta.get('progress', 0)
                    logger.info(f"  进度: {progress}% - {stage}")
            
            time.sleep(2)
        
        # 获取结果
        task_result = result.get()
        logger.info("向量化任务完成!")
        logger.info(f"文档数量: {task_result.get('document_count', 0)}")
        logger.info(f"总耗时: {task_result.get('total_time', 0):.2f}s")
        
        # 清理测试文件
        os.unlink(test_file_path)
        logger.info("测试文件已清理")
        
        logger.info("✅ 向量化任务测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量化任务测试失败: {e}")
        # 确保清理测试文件
        try:
            if 'test_file_path' in locals():
                os.unlink(test_file_path)
        except:
            pass
        return False


def test_environment_detection():
    """测试环境检测功能"""
    logger.info("=" * 60)
    logger.info("测试环境检测功能")
    logger.info("=" * 60)
    
    try:
        # 模拟不同环境
        environments = [
            ("默认环境", {}),
            ("Celery环境", {"CELERY_WORKER_RUNNING": "true"}),
            ("测试环境", {"PYTEST_CURRENT_TEST": "test_something"}),
        ]
        
        for env_name, env_vars in environments:
            logger.info(f"测试 {env_name}...")
            
            # 设置环境变量
            for key, value in env_vars.items():
                os.environ[key] = value
            
            # 重新导入以获取新的环境检测结果
            from services.worker_service.core.vectorizer import VectorizationProcessor
            processor = VectorizationProcessor()
            
            logger.info(f"  环境名称: {processor._get_environment_name()}")
            logger.info(f"  Celery环境: {processor._is_celery_env}")
            logger.info(f"  同步测试环境: {processor._is_sync_test_env}")
            
            # 清理
            processor.cleanup()
            
            # 清理环境变量
            for key in env_vars.keys():
                if key in os.environ:
                    del os.environ[key]
        
        logger.info("✅ 环境检测功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检测功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试Celery环境中的VectorizationProcessor")
    
    tests = [
        ("环境检测功能", test_environment_detection),
        ("健康检查任务", test_health_check_task),
        ("向量化任务", test_vectorization_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        return True
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
