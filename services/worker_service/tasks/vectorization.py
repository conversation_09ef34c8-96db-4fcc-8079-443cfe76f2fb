"""
向量化任务
处理文件向量化的Celery任务
"""

import time
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from shared.celery_config import celery_app
from shared.utils.logger import get_logger
from shared.models.file_info import FileInfo, FileStatus
from services.worker_service.core.vectorizer import VectorizationProcessor

logger = get_logger(__name__)


@celery_app.task(bind=True, name="worker.tasks.vectorize_file")
def vectorize_file(
    self,
    file_code: str,
    file_path: str,
    file_info: Dict[str, Any],
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
    force_reprocess: bool = False,
):
    """
    向量化文件任务

    Args:
        file_code: 文件编码
        file_path: 文件路径
        file_info: 文件信息字典
        chunk_size: 分块大小
        chunk_overlap: 分块重叠
        force_reprocess: 是否强制重新处理

    Returns:
        任务结果
    """

    async def _run_vectorization():
        """异步向量化的内部函数"""
        start_time = time.time()
        processor = None

        try:
            logger.info(f"开始向量化任务: {file_code} (任务ID: {self.request.id})")

            # 更新任务进度
            self.update_state(
                state="STARTED",
                meta={
                    "stage": "初始化",
                    "progress": 0,
                    "file_code": file_code,
                    "started_at": datetime.now().isoformat(),
                },
            )

            # 重建FileInfo对象
            try:
                # 处理datetime字段的反序列化
                if isinstance(file_info.get("created_at"), str):
                    file_info["created_at"] = datetime.fromisoformat(
                        file_info["created_at"]
                    )
                if isinstance(file_info.get("updated_at"), str):
                    file_info["updated_at"] = datetime.fromisoformat(
                        file_info["updated_at"]
                    )

                file_info_obj = FileInfo(**file_info)
                file_info_obj.status = FileStatus.VECTORIZING
                file_info_obj.updated_at = datetime.now()
            except Exception as e:
                logger.error(f"重建FileInfo对象失败: {e}")
                # 创建最小化的FileInfo对象
                file_info_obj = FileInfo(
                    file_code=file_code,
                    file_name=file_info.get("file_name", "unknown"),
                    file_path=file_path,
                    status=FileStatus.VECTORIZING,
                )

            # 创建向量化处理器
            processor = VectorizationProcessor()

            # 更新进度：开始文件处理
            self.update_state(
                state="PROGRESS",
                meta={
                    "stage": "文件解析",
                    "progress": 20,
                    "file_code": file_code,
                    "message": "正在解析文件内容",
                },
            )

            # 处理文件
            logger.info(f"开始文件解析: {file_code}")
            parse_start = time.time()
            documents = processor.process_file(file_path, file_code)
            parse_time = time.time() - parse_start

            logger.info(
                f"文件解析完成，生成 {len(documents)} 个文档块，耗时 {parse_time:.2f}s: {file_code}"
            )

            # 更新进度：开始向量化
            self.update_state(
                state="PROGRESS",
                meta={
                    "stage": "向量化处理",
                    "progress": 50,
                    "file_code": file_code,
                    "document_count": len(documents),
                    "message": f"正在生成 {len(documents)} 个文档的向量",
                },
            )

            # 存储向量
            logger.info(f"开始向量化存储: {file_code}")
            vector_start = time.time()
            success = processor.store_documents(file_code, documents, file_info_obj)
            vector_time = time.time() - vector_start

            if success:
                total_time = time.time() - start_time

                # 更新进度：完成
                self.update_state(
                    state="SUCCESS",
                    meta={
                        "stage": "完成",
                        "progress": 100,
                        "file_code": file_code,
                        "document_count": len(documents),
                        "parse_time": parse_time,
                        "vector_time": vector_time,
                        "total_time": total_time,
                        "completed_at": datetime.now().isoformat(),
                        "message": f"向量化完成，共处理 {len(documents)} 个文档块",
                    },
                )

                logger.info(f"向量化任务完成: {file_code}, 总耗时 {total_time:.2f}s")

                return {
                    "success": True,
                    "file_code": file_code,
                    "document_count": len(documents),
                    "processing_time": total_time,
                    "parse_time": parse_time,
                    "vector_time": vector_time,
                    "message": "向量化处理成功",
                }
            else:
                raise Exception("向量存储失败")

        except Exception as e:
            error_msg = str(e)
            total_time = time.time() - start_time

            logger.error(
                f"向量化任务失败 {file_code} (任务ID: {self.request.id}, 耗时 {total_time:.2f}s): {error_msg}"
            )

            # 更新任务状态为失败
            self.update_state(
                state="FAILURE",
                meta={
                    "stage": "失败",
                    "progress": 0,
                    "file_code": file_code,
                    "error": error_msg,
                    "total_time": total_time,
                    "failed_at": datetime.now().isoformat(),
                },
            )

            # 重新抛出异常，让Celery处理
            raise

        finally:
            # 清理资源
            if processor:
                try:
                    processor.cleanup()
                except Exception as cleanup_error:
                    logger.warning(f"清理向量化处理器资源时出错: {cleanup_error}")

    # 使用asyncio.run()执行异步向量化
    try:
        return asyncio.run(_run_vectorization())
    except Exception as e:
        # 这里的异常已经在内部函数中处理过了，直接重新抛出
        raise


@celery_app.task(bind=True, name="worker.tasks.process_documents")
def process_documents(self, file_code: str, documents: list, collection_name: str):
    """
    处理文档任务（备用任务，用于更细粒度的处理）

    Args:
        file_code: 文件编码
        documents: 文档列表
        collection_name: 集合名称

    Returns:
        处理结果
    """
    start_time = time.time()

    try:
        logger.info(
            f"开始文档处理任务: {file_code}, {len(documents)} 个文档 (任务ID: {self.request.id})"
        )

        # 更新任务进度
        self.update_state(
            state="STARTED",
            meta={
                "stage": "文档处理",
                "progress": 0,
                "file_code": file_code,
                "document_count": len(documents),
                "started_at": datetime.now().isoformat(),
            },
        )

        # 这里可以实现更细粒度的文档处理逻辑
        # 例如：批量处理、增量更新等

        # 模拟处理过程
        for i, doc in enumerate(documents):
            progress = int((i + 1) / len(documents) * 100)

            self.update_state(
                state="PROGRESS",
                meta={
                    "stage": "处理中",
                    "progress": progress,
                    "file_code": file_code,
                    "current_document": i + 1,
                    "total_documents": len(documents),
                },
            )

            # 实际的文档处理逻辑
            # ...

        total_time = time.time() - start_time

        # 完成
        self.update_state(
            state="SUCCESS",
            meta={
                "stage": "完成",
                "progress": 100,
                "file_code": file_code,
                "document_count": len(documents),
                "total_time": total_time,
                "completed_at": datetime.now().isoformat(),
            },
        )

        logger.info(f"文档处理任务完成: {file_code}, 耗时 {total_time:.2f}s")

        return {
            "success": True,
            "file_code": file_code,
            "document_count": len(documents),
            "processing_time": total_time,
            "message": "文档处理成功",
        }

    except Exception as e:
        error_msg = str(e)
        total_time = time.time() - start_time

        logger.error(
            f"文档处理任务失败 {file_code} (任务ID: {self.request.id}): {error_msg}"
        )

        # 更新任务状态为失败
        self.update_state(
            state="FAILURE",
            meta={
                "stage": "失败",
                "file_code": file_code,
                "error": error_msg,
                "total_time": total_time,
                "failed_at": datetime.now().isoformat(),
            },
        )

        raise
