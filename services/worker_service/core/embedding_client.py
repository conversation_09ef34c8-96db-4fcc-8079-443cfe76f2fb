"""
嵌入服务HTTP客户端
用于Worker服务调用独立的BGE-M3嵌入服务
"""

import asyncio
import httpx
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from services.worker_service.config import config

logger = get_logger(__name__)


class EmbeddingServiceClient:
    """嵌入服务HTTP客户端"""

    def __init__(
        self,
        base_url: str = None,
        timeout: float = 300.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        初始化嵌入服务客户端

        Args:
            base_url: 嵌入服务的基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        self.base_url = base_url or getattr(
            config, "embedding_service_url", "http://localhost:8004"
        )
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 创建HTTP客户端
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
        )

        # API端点
        self.api_prefix = "/api/v1"

        logger.info(f"嵌入服务客户端初始化: {self.base_url}")

    async def health_check(self) -> bool:
        """
        检查嵌入服务健康状态

        Returns:
            是否健康
        """
        try:
            response = await self.client.get(f"{self.api_prefix}/health/ready")
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"嵌入服务健康检查失败: {e}")
            return False

    async def embed_documents(
        self, texts: List[str], normalize: bool = True
    ) -> List[List[float]]:
        """
        批量文档嵌入

        Args:
            texts: 文本列表
            normalize: 是否标准化

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        # 准备请求数据
        request_data = {"texts": texts, "normalize": normalize}

        # 执行请求（带重试）
        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()

                response = await self.client.post(
                    f"{self.api_prefix}/embed/documents", json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    embeddings = result["embeddings"]

                    processing_time = time.time() - start_time
                    logger.debug(
                        f"批量嵌入成功: {len(texts)} 个文档, "
                        f"维度: {result.get('dimension', 0)}, "
                        f"耗时: {processing_time:.3f}s"
                    )

                    return embeddings
                else:
                    error_msg = f"嵌入服务返回错误: HTTP {response.status_code}"
                    if response.text:
                        error_msg += f", 详情: {response.text}"

                    if attempt < self.max_retries:
                        logger.warning(f"{error_msg}, 第 {attempt + 1} 次重试...")
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                        continue
                    else:
                        raise RuntimeError(error_msg)

            except httpx.TimeoutException:
                error_msg = f"嵌入服务请求超时 (超时时间: {self.timeout}s)"
                if attempt < self.max_retries:
                    logger.warning(f"{error_msg}, 第 {attempt + 1} 次重试...")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise RuntimeError(error_msg)

            except Exception as e:
                error_msg = f"嵌入服务请求失败: {e}"
                if attempt < self.max_retries:
                    logger.warning(f"{error_msg}, 第 {attempt + 1} 次重试...")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise RuntimeError(error_msg)

        # 不应该到达这里
        raise RuntimeError("嵌入请求失败，已达到最大重试次数")

    async def embed_query(self, text: str, normalize: bool = True) -> List[float]:
        """
        单个查询嵌入

        Args:
            text: 查询文本
            normalize: 是否标准化

        Returns:
            嵌入向量
        """
        # 准备请求数据
        request_data = {"text": text, "normalize": normalize}

        # 执行请求（带重试）
        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()

                response = await self.client.post(
                    f"{self.api_prefix}/embed/query", json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    embeddings = result["embeddings"]

                    processing_time = time.time() - start_time
                    logger.debug(
                        f"查询嵌入成功: 维度: {result.get('dimension', 0)}, "
                        f"耗时: {processing_time:.3f}s"
                    )

                    return embeddings[0] if embeddings else []
                else:
                    error_msg = f"嵌入服务返回错误: HTTP {response.status_code}"
                    if response.text:
                        error_msg += f", 详情: {response.text}"

                    if attempt < self.max_retries:
                        logger.warning(f"{error_msg}, 第 {attempt + 1} 次重试...")
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                        continue
                    else:
                        raise RuntimeError(error_msg)

            except Exception as e:
                error_msg = f"嵌入服务请求失败: {e}"
                if attempt < self.max_retries:
                    logger.warning(f"{error_msg}, 第 {attempt + 1} 次重试...")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise RuntimeError(error_msg)

        # 不应该到达这里
        raise RuntimeError("嵌入请求失败，已达到最大重试次数")

    async def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            模型信息
        """
        try:
            response = await self.client.get(f"{self.api_prefix}/embed/model/info")

            if response.status_code == 200:
                return response.json()
            else:
                raise RuntimeError(f"获取模型信息失败: HTTP {response.status_code}")

        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            raise

    async def close(self):
        """关闭客户端"""
        await self.client.aclose()

    def __del__(self):
        """析构函数 - 确保客户端被关闭"""
        try:
            if hasattr(self, "client") and self.client:
                # 在事件循环中关闭客户端
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.client.aclose())
                else:
                    asyncio.run(self.client.aclose())
        except Exception:
            pass  # 忽略析构时的错误


# 同步包装器，用于在非异步环境中使用
class SyncEmbeddingServiceClient:
    """嵌入服务同步客户端包装器"""

    def __init__(self, **kwargs):
        """初始化同步客户端"""
        self.async_client = EmbeddingServiceClient(**kwargs)
        self._loop = None

    def _get_loop(self):
        """获取或创建事件循环"""
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，我们不能使用run_until_complete
                # 这种情况下应该使用异步客户端
                raise RuntimeError("Cannot use sync client in running event loop")
            return loop
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop

    def health_check(self) -> bool:
        """同步健康检查"""
        loop = self._get_loop()
        return loop.run_until_complete(self.async_client.health_check())

    def embed_documents(
        self, texts: List[str], normalize: bool = True
    ) -> List[List[float]]:
        """同步批量文档嵌入"""
        loop = self._get_loop()
        return loop.run_until_complete(
            self.async_client.embed_documents(texts, normalize)
        )

    def embed_query(self, text: str, normalize: bool = True) -> List[float]:
        """同步查询嵌入"""
        loop = self._get_loop()
        return loop.run_until_complete(self.async_client.embed_query(text, normalize))

    def get_model_info(self) -> Dict[str, Any]:
        """同步获取模型信息"""
        loop = self._get_loop()
        return loop.run_until_complete(self.async_client.get_model_info())

    def close(self):
        """同步关闭客户端"""
        loop = self._get_loop()
        loop.run_until_complete(self.async_client.close())
