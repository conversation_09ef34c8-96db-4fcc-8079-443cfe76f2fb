#!/usr/bin/env python3
"""
测试改进后的VectorizationProcessor
验证环境感知功能和健康监测
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from services.worker_service.core.vectorizer import VectorizationProcessor

logger = get_logger(__name__)


def test_environment_detection():
    """测试环境检测功能"""
    logger.info("=" * 60)
    logger.info("测试环境检测功能")
    logger.info("=" * 60)
    
    try:
        processor = VectorizationProcessor()
        
        logger.info(f"Celery环境检测: {processor._is_celery_env}")
        logger.info(f"同步测试环境检测: {processor._is_sync_test_env}")
        logger.info(f"环境名称: {processor._get_environment_name()}")
        
        # 清理
        processor.cleanup()
        
        logger.info("✅ 环境检测测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检测测试失败: {e}")
        return False


def test_health_check():
    """测试健康检查功能"""
    logger.info("=" * 60)
    logger.info("测试健康检查功能")
    logger.info("=" * 60)
    
    try:
        processor = VectorizationProcessor()
        
        # 执行健康检查
        health_result = processor.health_check()
        
        logger.info("健康检查结果:")
        for key, value in health_result.items():
            logger.info(f"  {key}: {value}")
        
        # 清理
        processor.cleanup()
        
        logger.info("✅ 健康检查测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 健康检查测试失败: {e}")
        return False


def test_cleanup_methods():
    """测试清理方法"""
    logger.info("=" * 60)
    logger.info("测试清理方法")
    logger.info("=" * 60)
    
    try:
        processor = VectorizationProcessor()
        
        # 初始化嵌入服务
        embedding_service = processor._get_embedding_service()
        logger.info(f"嵌入服务初始化: {embedding_service is not None}")
        
        # 测试清理
        processor.cleanup()
        
        # 验证清理结果
        logger.info(f"清理后嵌入服务状态: {processor.embedding_service}")
        logger.info(f"清理后ChromaDB客户端状态: {processor.chroma_client}")
        
        logger.info("✅ 清理方法测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 清理方法测试失败: {e}")
        return False


def simulate_celery_environment():
    """模拟Celery环境"""
    logger.info("=" * 60)
    logger.info("模拟Celery环境测试")
    logger.info("=" * 60)
    
    try:
        # 设置Celery环境变量
        os.environ['CELERY_WORKER_RUNNING'] = 'true'
        
        processor = VectorizationProcessor()
        
        logger.info(f"模拟Celery环境检测: {processor._is_celery_env}")
        logger.info(f"环境名称: {processor._get_environment_name()}")
        
        # 执行健康检查
        health_result = processor.health_check()
        logger.info(f"Celery环境健康检查结果: {health_result.get('healthy', False)}")
        logger.info(f"环境类型: {health_result.get('environment', 'unknown')}")
        
        # 清理
        processor.cleanup()
        
        # 清理环境变量
        if 'CELERY_WORKER_RUNNING' in os.environ:
            del os.environ['CELERY_WORKER_RUNNING']
        
        logger.info("✅ Celery环境模拟测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ Celery环境模拟测试失败: {e}")
        # 确保清理环境变量
        if 'CELERY_WORKER_RUNNING' in os.environ:
            del os.environ['CELERY_WORKER_RUNNING']
        return False


def main():
    """主测试函数"""
    logger.info("开始测试改进后的VectorizationProcessor")
    
    tests = [
        ("环境检测", test_environment_detection),
        ("健康检查", test_health_check),
        ("清理方法", test_cleanup_methods),
        ("Celery环境模拟", simulate_celery_environment),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        return True
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
