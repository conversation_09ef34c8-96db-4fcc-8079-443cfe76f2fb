"""
风险分析API路由
保持与原分析服务的完全兼容性，但使用Celery异步处理
"""

from fastapi import APIRouter, HTTPException
from typing import Optional

from shared.utils.logger import get_logger
from services.unified_service.models.requests import AnalysisRequest
from services.unified_service.models.responses import AnalysisResponse
from services.unified_service.core.task_manager import TaskManager
from services.unified_service.core.chroma_client import ChromaClient
from shared.models.analysis_result import AnalysisStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/analyze", tags=["analyze"])

# 全局实例
task_manager = TaskManager()
chroma_client = ChromaClient()


@router.post("/{file_code}", response_model=AnalysisResponse)
async def analyze_risk(
    file_code: str,
    analysis_request: AnalysisRequest = AnalysisRequest()
):
    """
    开始风险分析
    保持与原分析服务API的完全兼容性
    
    Args:
        file_code: 文件编码
        analysis_request: 分析请求参数
        
    Returns:
        分析响应，包含分析ID和任务ID
    """
    try:
        logger.info(f"收到分析请求: {file_code}")
        
        # 检查文件是否存在于向量数据库中
        file_exists = await chroma_client.check_file_exists(file_code)
        if not file_exists:
            raise HTTPException(
                status_code=404, 
                detail=f"File {file_code} not found in vector database"
            )
        
        # 提交异步分析任务
        task_result = await task_manager.submit_analysis_task(
            file_code=file_code,
            analysis_type=analysis_request.analysis_type,
            options=analysis_request.options,
            priority=analysis_request.priority
        )
        
        # 生成分析ID（与任务ID相同，保持兼容性）
        analysis_id = task_result.id
        
        logger.info(f"分析任务已提交: {file_code}, 分析ID: {analysis_id}")
        
        return AnalysisResponse(
            success=True,
            message="Analysis task submitted successfully",
            analysis_id=analysis_id,
            file_code=file_code,
            status=AnalysisStatus.PENDING,
            estimated_time=1800,  # 预估30分钟
            task_id=task_result.id,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/{file_code}/quick")
async def quick_analysis(file_code: str):
    """
    快速分析（简化版本）
    保持与原分析服务API的兼容性
    
    Args:
        file_code: 文件编码
        
    Returns:
        快速分析结果
    """
    try:
        logger.info(f"收到快速分析请求: {file_code}")
        
        # 检查文件是否存在
        file_exists = await chroma_client.check_file_exists(file_code)
        if not file_exists:
            raise HTTPException(
                status_code=404, 
                detail=f"File {file_code} not found"
            )
        
        # 提交快速分析任务（高优先级）
        task_result = await task_manager.submit_analysis_task(
            file_code=file_code,
            analysis_type="quick",
            priority=5  # 高优先级
        )
        
        # 对于快速分析，我们可以选择等待结果或立即返回
        # 这里保持异步模式，返回任务信息
        return {
            "success": True,
            "file_code": file_code,
            "analysis_id": task_result.id,
            "task_id": task_result.id,
            "status": "submitted",
            "message": "Quick analysis task submitted",
            "estimated_time": 300,  # 预估5分钟
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"快速分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"Quick analysis failed: {str(e)}")


@router.post("/batch")
async def batch_analysis(file_codes: list[str], analysis_type: str = "default"):
    """
    批量分析
    
    Args:
        file_codes: 文件编码列表
        analysis_type: 分析类型
        
    Returns:
        批量分析响应
    """
    try:
        logger.info(f"收到批量分析请求: {len(file_codes)} 个文件")
        
        results = []
        task_ids = []
        
        for file_code in file_codes:
            try:
                # 检查文件是否存在
                file_exists = await chroma_client.check_file_exists(file_code)
                if not file_exists:
                    results.append({
                        "file_code": file_code,
                        "status": "error",
                        "error": "File not found in vector database"
                    })
                    continue
                
                # 提交分析任务
                task_result = await task_manager.submit_analysis_task(
                    file_code=file_code,
                    analysis_type=analysis_type,
                    priority=1
                )
                
                results.append({
                    "file_code": file_code,
                    "analysis_id": task_result.id,
                    "task_id": task_result.id,
                    "status": "submitted"
                })
                
                task_ids.append(task_result.id)
                
            except Exception as e:
                logger.error(f"处理文件 {file_code} 失败: {e}")
                results.append({
                    "file_code": file_code,
                    "status": "error",
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Batch analysis submitted: {len(results)} files processed",
            "results": results,
            "task_ids": task_ids,
            "total_files": len(file_codes),
            "successful_submissions": len([r for r in results if r.get("status") == "submitted"])
        }
        
    except Exception as e:
        logger.error(f"批量分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")


@router.get("/{file_code}/status")
async def get_analysis_status(file_code: str):
    """
    获取文件的分析状态
    
    Args:
        file_code: 文件编码
        
    Returns:
        分析状态信息
    """
    try:
        # 这里需要查询与该文件相关的所有分析任务
        # 暂时返回基本信息
        logger.info(f"查询分析状态: {file_code}")
        
        return {
            "file_code": file_code,
            "status": "not_implemented",
            "message": "Analysis status query not fully implemented yet"
        }
        
    except Exception as e:
        logger.error(f"查询分析状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Status query failed: {str(e)}")
