"""
文件上传API路由
使用Celery异步处理文件向量化
"""

from typing import Optional
from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse

from shared.utils.logger import get_logger
from services.unified_service.models.responses import UploadResponse
from services.unified_service.core.file_manager import FileManager
from services.unified_service.core.task_manager import TaskManager
from shared.models.file_info import FileStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/upload", tags=["upload"])

# 全局实例
file_manager = FileManager()
task_manager = TaskManager()


@router.post("/", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    force_reprocess: bool = False,
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
):
    """
    上传文件并启动异步向量化处理
    
    Args:
        file: 上传的文件
        force_reprocess: 是否强制重新处理
        chunk_size: 文档分块大小
        chunk_overlap: 文档分块重叠
        
    Returns:
        上传响应，包含文件编码和任务ID
    """
    try:
        logger.info(f"收到文件上传请求: {file.filename}")
        
        # 读取文件数据
        file_data = await file.read()
        
        # 保存文件并获取文件信息
        file_path, file_info = file_manager.save_uploaded_file(
            file_data, file.filename
        )
        
        logger.info(f"文件保存成功: {file_info.file_code}")
        
        # 检查是否已存在且不需要重新处理
        existing_status = await task_manager.get_file_status(file_info.file_code)
        if existing_status and not force_reprocess:
            if existing_status.status == FileStatus.COMPLETED:
                return UploadResponse(
                    success=True,
                    message="File already processed",
                    file_code=file_info.file_code,
                    file_name=file_info.file_name,
                    file_size=file_info.file_size,
                    status=existing_status.status,
                )
        
        # 提交异步向量化任务
        task_result = await task_manager.submit_vectorization_task(
            file_code=file_info.file_code,
            file_path=file_path,
            file_info=file_info,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            force_reprocess=force_reprocess
        )
        
        logger.info(f"向量化任务已提交: {file_info.file_code}, 任务ID: {task_result.id}")
        
        return UploadResponse(
            success=True,
            message="File uploaded successfully, vectorization task submitted",
            file_code=file_info.file_code,
            file_name=file_info.file_name,
            file_size=file_info.file_size,
            status=FileStatus.PROCESSING,
            task_id=task_result.id,
        )
        
    except ValueError as e:
        logger.error(f"File upload validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.post("/batch")
async def upload_batch_files(
    files: list[UploadFile] = File(...),
    force_reprocess: bool = False,
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
):
    """
    批量上传文件
    
    Args:
        files: 上传的文件列表
        force_reprocess: 是否强制重新处理
        chunk_size: 文档分块大小
        chunk_overlap: 文档分块重叠
        
    Returns:
        批量上传响应
    """
    try:
        logger.info(f"收到批量文件上传请求: {len(files)} 个文件")
        
        results = []
        task_ids = []
        
        for file in files:
            try:
                # 读取文件数据
                file_data = await file.read()
                
                # 保存文件
                file_path, file_info = file_manager.save_uploaded_file(
                    file_data, file.filename
                )
                
                # 提交异步任务
                task_result = await task_manager.submit_vectorization_task(
                    file_code=file_info.file_code,
                    file_path=file_path,
                    file_info=file_info,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    force_reprocess=force_reprocess
                )
                
                results.append({
                    "file_name": file.filename,
                    "file_code": file_info.file_code,
                    "task_id": task_result.id,
                    "status": "submitted"
                })
                
                task_ids.append(task_result.id)
                
            except Exception as e:
                logger.error(f"处理文件 {file.filename} 失败: {e}")
                results.append({
                    "file_name": file.filename,
                    "error": str(e),
                    "status": "failed"
                })
        
        return {
            "success": True,
            "message": f"Batch upload completed: {len(results)} files processed",
            "results": results,
            "task_ids": task_ids,
            "total_files": len(files),
            "successful_uploads": len([r for r in results if "error" not in r])
        }
        
    except Exception as e:
        logger.error(f"Batch upload error: {e}")
        raise HTTPException(status_code=500, detail=f"Batch upload failed: {str(e)}")
