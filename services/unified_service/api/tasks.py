"""
任务管理API路由
提供Celery任务状态查询和管理功能
"""

from fastapi import APIRouter, HTTPException
from typing import Optional

from shared.utils.logger import get_logger
from services.unified_service.models.responses import TaskStatusResponse
from services.unified_service.core.task_manager import TaskManager

logger = get_logger(__name__)

router = APIRouter(prefix="/tasks", tags=["tasks"])

# 全局实例
task_manager = TaskManager()


@router.get("/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str, include_result: bool = False):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        include_result: 是否包含结果详情
        
    Returns:
        任务状态响应
    """
    try:
        logger.info(f"查询任务状态: {task_id}")
        
        # 获取任务状态
        status_info = await task_manager.get_task_status(task_id)
        
        response_data = {
            "success": True,
            "message": "Task status retrieved",
            "task_id": task_id,
            "status": status_info.get("status", "UNKNOWN"),
            "error": status_info.get("error"),
            "progress": status_info.get("progress"),
            "created_at": status_info.get("created_at"),
            "started_at": status_info.get("started_at"),
            "completed_at": status_info.get("completed_at"),
        }
        
        # 根据参数决定是否包含结果
        if include_result:
            response_data["result"] = status_info.get("result")
        
        return TaskStatusResponse(**response_data)
        
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Task status query failed: {str(e)}")


@router.post("/{task_id}/cancel")
async def cancel_task(task_id: str):
    """
    取消任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        取消结果
    """
    try:
        logger.info(f"取消任务: {task_id}")
        
        success = await task_manager.cancel_task(task_id)
        
        if success:
            return {
                "success": True,
                "message": f"Task {task_id} cancelled successfully",
                "task_id": task_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to cancel task {task_id}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"Cancel task failed: {str(e)}")


@router.post("/{task_id}/retry")
async def retry_task(task_id: str):
    """
    重试失败的任务
    
    Args:
        task_id: 原任务ID
        
    Returns:
        重试结果
    """
    try:
        logger.info(f"重试任务: {task_id}")
        
        new_task = await task_manager.retry_failed_task(task_id)
        
        if new_task:
            return {
                "success": True,
                "message": f"Task {task_id} retried successfully",
                "original_task_id": task_id,
                "new_task_id": new_task.id
            }
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot retry task {task_id} (task may not be failed or not found)"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"Retry task failed: {str(e)}")


@router.get("/")
async def get_queue_stats():
    """
    获取队列统计信息
    
    Returns:
        队列统计信息
    """
    try:
        logger.info("获取队列统计信息")
        
        stats = await task_manager.get_queue_stats()
        
        return {
            "success": True,
            "message": "Queue statistics retrieved",
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"获取队列统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"Queue stats query failed: {str(e)}")


@router.get("/batch/{task_ids}")
async def get_batch_task_status(task_ids: str):
    """
    批量查询任务状态
    
    Args:
        task_ids: 逗号分隔的任务ID列表
        
    Returns:
        批量任务状态响应
    """
    try:
        task_id_list = [tid.strip() for tid in task_ids.split(",")]
        logger.info(f"批量查询任务状态: {len(task_id_list)} 个任务")
        
        results = []
        
        for task_id in task_id_list:
            try:
                status_info = await task_manager.get_task_status(task_id)
                results.append({
                    "task_id": task_id,
                    "status": status_info.get("status", "UNKNOWN"),
                    "error": status_info.get("error"),
                    "success": True
                })
                
            except Exception as e:
                logger.error(f"查询任务状态失败 {task_id}: {e}")
                results.append({
                    "task_id": task_id,
                    "status": "ERROR",
                    "error": str(e),
                    "success": False
                })
        
        return {
            "success": True,
            "message": f"Batch task status query completed: {len(results)} tasks",
            "results": results,
            "total_tasks": len(task_id_list),
            "successful_queries": len([r for r in results if r.get("success")])
        }
        
    except Exception as e:
        logger.error(f"批量查询任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Batch task status query failed: {str(e)}")


@router.get("/monitor/dashboard")
async def get_monitoring_dashboard():
    """
    获取监控面板数据
    
    Returns:
        监控面板数据
    """
    try:
        logger.info("获取监控面板数据")
        
        # 获取队列统计
        queue_stats = await task_manager.get_queue_stats()
        
        # 构建监控数据
        dashboard_data = {
            "queue_stats": queue_stats,
            "system_info": {
                "service": "unified-service",
                "version": "1.0.0",
                "status": "running"
            },
            "recent_activity": {
                "message": "Recent activity tracking not implemented yet"
            }
        }
        
        return {
            "success": True,
            "message": "Monitoring dashboard data retrieved",
            "dashboard": dashboard_data
        }
        
    except Exception as e:
        logger.error(f"获取监控面板数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"Dashboard query failed: {str(e)}")
