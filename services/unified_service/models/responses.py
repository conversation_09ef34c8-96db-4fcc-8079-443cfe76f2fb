"""
统一服务响应模型
合并了原有分析服务和向量化服务的响应模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from datetime import datetime
from shared.models.common import BaseResponse
from shared.models.analysis_result import AnalysisResult, AnalysisStatus
from shared.models.file_info import FileStatus


class UploadResponse(BaseResponse):
    """文件上传响应 - 来自向量化服务"""
    file_code: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    status: Optional[FileStatus] = None
    task_id: Optional[str] = None  # Celery任务ID


class AnalysisResponse(BaseResponse):
    """分析响应 - 来自分析服务"""
    analysis_id: Optional[str] = None
    file_code: Optional[str] = None
    status: Optional[AnalysisStatus] = None
    estimated_time: Optional[int] = None  # 预估完成时间(秒)
    task_id: Optional[str] = None  # Celery任务ID


class AnalysisResultResponse(BaseResponse):
    """分析结果响应 - 来自分析服务"""
    analysis_result: Optional[AnalysisResult] = None


class BatchAnalysisResponse(BaseResponse):
    """批量分析响应 - 来自分析服务"""
    batch_id: Optional[str] = None
    analysis_ids: Optional[List[str]] = None
    total_count: Optional[int] = None
    status: str = "submitted"
    task_ids: Optional[List[str]] = None  # Celery任务ID列表


class StatusResponse(BaseResponse):
    """状态响应 - 来自向量化服务"""
    file_code: Optional[str] = None
    status: Optional[FileStatus] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None  # Celery任务ID
    task_status: Optional[str] = None  # Celery任务状态


class FileListResponse(BaseResponse):
    """文件列表响应 - 来自分析服务"""
    files: Optional[List[Dict[str, Any]]] = None
    total: Optional[int] = None


class HealthResponse(BaseModel):
    """健康检查响应 - 合并两个服务的健康检查"""
    status: str = "healthy"
    service: str = "unified-service"
    version: str = "1.0.0"
    timestamp: datetime = datetime.now()
    checks: Dict[str, bool] = {}
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TaskStatusResponse(BaseResponse):
    """任务状态响应"""
    task_id: str
    status: str  # PENDING, STARTED, SUCCESS, FAILURE, RETRY, REVOKED
    result: Optional[Any] = None
    error: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
