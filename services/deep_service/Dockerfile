# Deep服务独立 Dockerfile
# API网关和统一服务

FROM python:3.10-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r deep && useradd -r -g deep deep

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/data/uploads /app/logs /app/cache && \
    chown -R deep:deep /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV UPLOAD_DIR=/app/data/uploads
ENV LOG_FILE=/app/logs/deep_service.log

# 切换到非root用户
USER deep

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
