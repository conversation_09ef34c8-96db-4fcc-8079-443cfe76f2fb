"""
Deep服务配置
合并了原有的分析服务和向量化服务配置
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class DeepServiceConfig(BaseSettings):
    """Deep服务配置"""

    # 服务基本信息
    service_name: str = "deep-service"
    service_version: str = "1.0.0"
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", "8000"))
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # 文件上传配置
    upload_dir: str = os.getenv("UPLOAD_DIR", "./data/uploads")
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "100"))  # MB
    allowed_extensions: set = {".csv", ".xlsx", ".xls"}

    # ChromaDB配置 (客户端模式，连接到独立的ChromaDB服务)
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8001"))
    chroma_api_version: str = os.getenv("CHROMA_API_VERSION", "v1")  # 默认v1保持兼容性
    chroma_collection_prefix: str = os.getenv("CHROMA_COLLECTION_PREFIX", "file_")

    # Redis配置由统一的redis_config管理

    # Celery配置
    celery_broker_url: str = os.getenv("CELERY_BROKER_URL", "")
    celery_result_backend: str = os.getenv("CELERY_RESULT_BACKEND", "")

    # LLM配置 (保持与原分析服务一致)
    llm_provider: str = os.getenv("LLM_PROVIDER", "deepseek")
    deepseek_api_key: str = os.getenv("DEEPSEEK_API_KEY", "")
    deepseek_model: str = os.getenv("DEEPSEEK_MODEL", "deepseek-reasoner")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4")
    enable_streaming: bool = os.getenv("ENABLE_STREAMING", "true").lower() == "true"

    # 提示词配置
    prompts_dir: str = os.getenv("PROMPTS_DIR", "./prompts")

    # 任务配置
    task_timeout: int = int(os.getenv("TASK_TIMEOUT", "1800"))  # 30分钟
    task_retry_max: int = int(os.getenv("TASK_RETRY_MAX", "3"))
    task_retry_delay: int = int(os.getenv("TASK_RETRY_DELAY", "60"))  # 60秒

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 自动构建Redis URL (使用统一配置)
        if not self.celery_broker_url:
            from shared.redis_config import get_redis_url
            self.celery_broker_url = get_redis_url()
        
        if not self.celery_result_backend:
            self.celery_result_backend = self.celery_broker_url

    @property
    def chroma_url(self) -> str:
        """ChromaDB服务URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    @property
    def redis_url(self) -> str:
        """Redis连接URL (使用统一配置)"""
        from shared.redis_config import get_redis_url
        return get_redis_url()

    def build_chroma_api_path(self, endpoint: str) -> str:
        """构建ChromaDB API路径"""
        return f"/api/{self.chroma_api_version}/{endpoint.lstrip('/')}"

    def validate_chroma_api_version(self) -> bool:
        """验证ChromaDB API版本有效性"""
        valid_versions = ["v1", "v2"]
        is_valid = self.chroma_api_version in valid_versions
        
        if not is_valid:
            import warnings
            warnings.warn(
                f"ChromaDB API版本 '{self.chroma_api_version}' 不在支持的版本列表中: {valid_versions}。"
                f"请检查配置或更新到支持的版本。",
                UserWarning
            )
        
        return is_valid

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量


# 创建全局配置实例
config = DeepServiceConfig()

# 启动时验证配置
config.validate_chroma_api_version()
