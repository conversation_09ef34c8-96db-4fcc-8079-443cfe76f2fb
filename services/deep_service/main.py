"""
Deep服务主应用
合并了原有分析服务和向量化服务的功能
"""

import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from shared.utils.logger import setup_logging, get_logger
from services.deep_service.config import config
from services.deep_service.api import (
    health_router,
    upload_router,
    analyze_router,
    status_router,
    files_router,
    tasks_router,
    results_router,
)

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    # 启动时执行
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)
    
    # 服务配置信息
    logger.info(f"📡 服务地址: {config.host}:{config.port}")
    logger.info(f"🐛 调试模式: {config.debug}")
    logger.info(f"📝 日志级别: {config.log_level}")
    
    # 依赖服务配置
    logger.info(f"🗄️  ChromaDB: {config.chroma_url}")
    logger.info(f"🔴 Redis: {config.redis_url}")
    logger.info(f"🤖 LLM提供商: {config.llm_provider}")
    logger.info(f"📊 流式输出: {config.enable_streaming}")
    
    # 文件上传配置
    logger.info(f"📂 上传目录: {config.upload_dir}")
    logger.info(f"📏 最大文件大小: {config.max_file_size}MB")
    logger.info(f"📋 支持的文件类型: {config.allowed_extensions}")
    
    # Celery配置
    logger.info(f"⚙️  Celery Broker: {config.celery_broker_url}")
    logger.info(f"📦 任务超时: {config.task_timeout}秒")
    logger.info(f"🔄 最大重试次数: {config.task_retry_max}")
    
    logger.info("✅ Deep服务启动完成")
    logger.info("=" * 60)
    
    yield
    
    # 关闭时执行
    logger.info("=" * 60)
    logger.info(f"🛑 关闭 {config.service_name}")
    
    # 清理资源
    try:
        # 这里可以添加资源清理逻辑
        # 例如关闭数据库连接、清理临时文件等
        logger.info("🧹 清理资源...")
        
        logger.info("✅ 资源清理完成")
        
    except Exception as e:
        logger.error(f"❌ 资源清理失败: {e}")
    
    logger.info("✅ 服务关闭完成")
    logger.info("=" * 60)


# 创建FastAPI应用
app = FastAPI(
    title="Deep Risk RAG Deep Service",
    description="Deep服务 - 集成文件上传、向量化、风险分析和LLM调用的完整解决方案",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router)
app.include_router(upload_router)
app.include_router(analyze_router)
app.include_router(status_router)
app.include_router(files_router)
app.include_router(tasks_router)
app.include_router(results_router)


# 添加根路径重定向到API文档
@app.get("/")
async def root():
    """
    根路径 - 返回服务信息
    """
    return {
        "service": "deep-service",
        "version": "1.0.0",
        "description": "Deep Risk RAG Deep Service",
        "status": "running",
        "features": [
            "异步文件上传和向量化",
            "基于Celery的任务队列",
            "风险分析和预测",
            "多种LLM支持",
            "实时任务状态跟踪",
            "统一的API接口",
        ],
        "endpoints": {
            # 文件上传相关 (原向量化服务)
            "upload": "/upload/",
            "upload_batch": "/upload/batch",
            "upload_status": "/status/{file_code}",
            
            # 风险分析相关 (原分析服务)
            "analyze": "/analyze/{file_code}",
            "analyze_quick": "/analyze/{file_code}/quick",
            "analyze_batch": "/analyze/batch",
            "analysis_result": "/result/{analysis_id}",
            "analysis_status": "/result/{analysis_id}/status",
            
            # 文件管理
            "files": "/files/",
            "file_details": "/files/{file_code}",
            "file_documents": "/files/{file_code}/documents",
            
            # 任务管理 (新增)
            "task_status": "/tasks/{task_id}",
            "task_cancel": "/tasks/{task_id}/cancel",
            "task_retry": "/tasks/{task_id}/retry",
            "queue_stats": "/tasks/",
            
            # 系统相关
            "health": "/health",
            "docs": "/docs",
            "redoc": "/redoc",
        },
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc",
            "openapi_json": "/openapi.json",
        }
    }


if __name__ == "__main__":
    # 运行服务
    uvicorn.run(
        "main:app",
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower(),
    )
