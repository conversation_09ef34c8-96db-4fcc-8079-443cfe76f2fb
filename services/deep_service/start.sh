#!/bin/bash

# Deep服务独立启动脚本
# API网关和统一服务启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Deep服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -d, --dev         开发模式启动"
    echo "  -p, --prod        生产模式启动"
    echo "  --docker          Docker模式启动"
    echo "  --check           检查环境和依赖"
    echo "  --install         安装依赖"
    echo "  --test            运行测试"
    echo ""
    echo "环境变量:"
    echo "  HOST              服务主机地址 (默认: 0.0.0.0)"
    echo "  PORT              服务端口 (默认: 8000)"
    echo "  LOG_LEVEL         日志级别 (默认: INFO)"
    echo "  REDIS_URL         Redis连接URL"
    echo "  CHROMA_HOST       ChromaDB主机地址"
    echo "  DEEPSEEK_API_KEY  DeepSeek API密钥"
    echo ""
}

# 检查Python环境
check_python() {
    print_header "🐍 检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_message "Python版本: $python_version"
    
    # 检查Python版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
        print_error "需要Python 3.10或更高版本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_header "📦 检查依赖..."
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 检查关键依赖
    python3 -c "
import sys
try:
    import fastapi
    import uvicorn
    import celery
    import redis
    import httpx
    import pandas
    print('✅ 关键依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少依赖: {e}')
    sys.exit(1)
"
}

# 检查外部服务
check_external_services() {
    print_header "🔗 检查外部服务..."
    
    # 检查Redis
    redis_host=${REDIS_HOST:-localhost}
    redis_port=${REDIS_PORT:-6379}
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h $redis_host -p $redis_port ping &> /dev/null; then
            print_message "Redis连接正常: $redis_host:$redis_port"
        else
            print_warning "Redis连接失败: $redis_host:$redis_port"
        fi
    else
        print_warning "redis-cli未安装，无法检查Redis连接"
    fi
    
    # 检查ChromaDB
    chroma_host=${CHROMA_HOST:-localhost}
    chroma_port=${CHROMA_PORT:-8001}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "http://$chroma_host:$chroma_port/api/v1/heartbeat" &> /dev/null; then
            print_message "ChromaDB连接正常: $chroma_host:$chroma_port"
        else
            print_warning "ChromaDB连接失败: $chroma_host:$chroma_port"
        fi
    else
        print_warning "curl未安装，无法检查ChromaDB连接"
    fi
    
    # 检查嵌入服务
    embedding_host=${EMBEDDING_SERVICE_HOST:-localhost}
    embedding_port=${EMBEDDING_SERVICE_PORT:-8004}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "http://$embedding_host:$embedding_port/api/v1/health/ready" &> /dev/null; then
            print_message "嵌入服务连接正常: $embedding_host:$embedding_port"
        else
            print_warning "嵌入服务连接失败: $embedding_host:$embedding_port"
        fi
    fi
}

# 检查配置
check_configuration() {
    print_header "⚙️ 检查配置..."
    
    # 检查必需的环境变量
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        print_warning "DEEPSEEK_API_KEY未设置"
    else
        print_message "DEEPSEEK_API_KEY已设置"
    fi
    
    # 检查上传目录
    upload_dir=${UPLOAD_DIR:-./data/uploads}
    if [ ! -d "$upload_dir" ]; then
        print_message "创建上传目录: $upload_dir"
        mkdir -p "$upload_dir"
    else
        print_message "上传目录存在: $upload_dir"
    fi
}

# 安装依赖
install_dependencies() {
    print_header "📦 安装依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    print_message "安装Python依赖..."
    pip3 install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 运行测试
run_tests() {
    print_header "🧪 运行测试..."
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
    else
        print_warning "pytest未安装，跳过测试"
    fi
}

# 设置环境变量
setup_environment() {
    print_header "⚙️ 设置环境..."
    
    # 设置默认环境变量
    export HOST=${HOST:-"0.0.0.0"}
    export PORT=${PORT:-"8000"}
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/../.."
    
    print_message "服务地址: $HOST:$PORT"
    print_message "日志级别: $LOG_LEVEL"
    print_message "Python路径: $PYTHONPATH"
}

# 启动服务
start_service() {
    local mode=$1
    
    print_header "🚀 启动Deep服务..."
    
    case $mode in
        dev)
            print_message "开发模式启动..."
            export DEBUG=true
            python3 -m uvicorn main:app --host $HOST --port $PORT --reload
            ;;
        prod)
            print_message "生产模式启动..."
            export DEBUG=false
            python3 -m uvicorn main:app --host $HOST --port $PORT --workers 4
            ;;
        docker)
            print_message "Docker模式启动..."
            export DEBUG=false
            exec python3 -m uvicorn main:app --host $HOST --port $PORT
            ;;
        *)
            print_message "标准模式启动..."
            python3 -m uvicorn main:app --host $HOST --port $PORT
            ;;
    esac
}

# 主函数
main() {
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        --check)
            check_python
            check_dependencies
            check_external_services
            check_configuration
            print_message "环境检查完成"
            ;;
        --install)
            check_python
            install_dependencies
            ;;
        --test)
            check_dependencies
            run_tests
            ;;
        -d|--dev)
            setup_environment
            check_dependencies
            check_configuration
            start_service "dev"
            ;;
        -p|--prod)
            setup_environment
            check_dependencies
            check_configuration
            start_service "prod"
            ;;
        --docker)
            setup_environment
            start_service "docker"
            ;;
        *)
            setup_environment
            check_dependencies
            check_configuration
            start_service "standard"
            ;;
    esac
}

# 执行主函数
main "$@"
