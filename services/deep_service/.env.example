# Deep服务环境配置示例
# API网关和统一服务配置

# =============================================================================
# 服务基本配置
# =============================================================================
# 服务名称
SERVICE_NAME=deep-service

# 服务版本
SERVICE_VERSION=1.0.0

# 服务地址和端口
HOST=0.0.0.0
PORT=8000

# 运行模式
DEBUG=false
LOG_LEVEL=INFO

# 部署环境 (development/staging/production)
ENVIRONMENT=development

# 部署模式 (standalone/docker/k8s)
DEPLOYMENT_MODE=standalone

# =============================================================================
# 外部服务配置
# =============================================================================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_API_VERSION=v1
CHROMA_URL=http://localhost:8001

# 嵌入服务配置
EMBEDDING_SERVICE_HOST=localhost
EMBEDDING_SERVICE_PORT=8004
EMBEDDING_SERVICE_URL=http://localhost:8004

# =============================================================================
# LLM配置
# =============================================================================
# LLM提供商 (deepseek/openai/claude/gemini)
LLM_PROVIDER=deepseek

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 模型配置
LLM_MODEL=deepseek-chat
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# 流式输出
ENABLE_STREAMING=true

# =============================================================================
# 文件上传配置
# =============================================================================
# 上传目录
UPLOAD_DIR=./data/uploads

# 最大文件大小 (MB)
MAX_FILE_SIZE=100

# 允许的文件类型 (逗号分隔)
ALLOWED_EXTENSIONS=.csv,.xlsx,.xls

# 文件保留时间 (天)
FILE_RETENTION_DAYS=30

# =============================================================================
# 任务配置
# =============================================================================
# 任务超时时间 (秒)
TASK_TIMEOUT=3600

# 最大重试次数
TASK_RETRY_MAX=3

# 任务队列名称
TASK_QUEUE_NAME=default

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# 缓存配置
# =============================================================================
# 启用缓存
ENABLE_CACHE=true

# 缓存TTL (秒)
CACHE_TTL=3600

# 最大缓存大小
MAX_CACHE_SIZE=1000

# =============================================================================
# 安全配置
# =============================================================================
# API密钥 (可选)
# API_KEY=your_api_key_here

# JWT密钥 (可选)
# JWT_SECRET_KEY=your_jwt_secret_here

# 允许的来源 (CORS)
ALLOWED_ORIGINS=*

# 请求速率限制 (每分钟)
RATE_LIMIT=100

# =============================================================================
# 监控配置
# =============================================================================
# 启用Prometheus指标
ENABLE_METRICS=false

# Prometheus端口
METRICS_PORT=9090

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# 日志配置
# =============================================================================
# 日志格式 (json/text)
LOG_FORMAT=text

# 日志文件路径
LOG_FILE=./logs/deep_service.log

# 日志轮转大小 (MB)
LOG_MAX_SIZE=100

# 保留日志文件数量
LOG_BACKUP_COUNT=5

# =============================================================================
# 向量化配置
# =============================================================================
# 文档分块大小
CHUNK_SIZE=1000

# 分块重叠
CHUNK_OVERLAP=200

# 检索Top-K
RETRIEVAL_TOP_K=5

# 向量化批处理大小
VECTORIZATION_BATCH_SIZE=32

# =============================================================================
# 分析配置
# =============================================================================
# 分析超时时间 (秒)
ANALYSIS_TIMEOUT=1800

# 最大并发分析数
MAX_CONCURRENT_ANALYSES=5

# 分析结果保留时间 (天)
ANALYSIS_RETENTION_DAYS=90

# =============================================================================
# Docker特定配置
# =============================================================================
# 容器内上传路径
DOCKER_UPLOAD_PATH=/app/data/uploads

# 容器内日志路径
DOCKER_LOG_PATH=/app/logs

# 容器内缓存路径
DOCKER_CACHE_PATH=/app/cache

# =============================================================================
# 开发配置
# =============================================================================
# 开发模式下的额外配置
DEV_RELOAD=true
DEV_DEBUG_SQL=false
DEV_MOCK_SERVICES=false

# 测试模式
TEST_MODE=false
TEST_DATABASE_URL=sqlite:///./test.db
