# Deep服务独立依赖
# API网关和统一服务依赖

# 核心框架
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 异步任务队列
celery>=5.3.0
redis>=5.0.0

# HTTP客户端和网络
httpx>=0.25.0
aiofiles>=23.2.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 文件处理
openpyxl>=3.1.0
xlrd>=2.0.1

# 日志和配置
python-dotenv>=1.0.0

# 系统监控
psutil>=5.9.0

# 数据验证和序列化
email-validator>=2.1.0

# 安全和认证
python-multipart>=0.0.6

# 缓存支持
cachetools>=5.3.0

# 时间处理
python-dateutil>=2.8.2

# 文件类型检测
python-magic>=0.4.27

# 压缩和解压
zipfile36>=0.1.3

# 可选：监控和指标
# prometheus-client>=0.17.0
# grafana-api>=1.0.0

# 可选：安全增强
# python-jose[cryptography]>=3.3.0
# passlib[bcrypt]>=1.7.4

# 开发和测试工具（开发环境）
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# pytest-httpx>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0
