"""
统一错误处理器
为Deep服务提供统一的错误处理和响应格式
"""

import traceback
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import Request, HTTPException
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

# 尝试导入共享组件
try:
    from shared.utils.logger import get_logger
    from shared.constants import ErrorCodes
    SHARED_AVAILABLE = True
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class ErrorCodes:
        INTERNAL_ERROR = "INTERNAL_ERROR"
        INVALID_REQUEST = "INVALID_REQUEST"
        NOT_FOUND = "NOT_FOUND"
        FILE_NOT_FOUND = "FILE_NOT_FOUND"
        FILE_TOO_LARGE = "FILE_TOO_LARGE"
        INVALID_FILE_TYPE = "INVALID_FILE_TYPE"
        SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
        TASK_FAILED = "TASK_FAILED"
    
    SHARED_AVAILABLE = False

logger = get_logger(__name__)


class DeepServiceError(Exception):
    """Deep服务基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = ErrorCodes.INTERNAL_ERROR,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)


class FileNotFoundError(DeepServiceError):
    """文件未找到异常"""
    
    def __init__(self, file_code: str, message: str = None):
        message = message or f"File not found: {file_code}"
        super().__init__(
            message=message,
            error_code=ErrorCodes.FILE_NOT_FOUND,
            status_code=404,
            details={"file_code": file_code}
        )


class FileTooLargeError(DeepServiceError):
    """文件过大异常"""
    
    def __init__(self, file_size: int, max_size: int):
        message = f"File size {file_size}MB exceeds maximum {max_size}MB"
        super().__init__(
            message=message,
            error_code=ErrorCodes.FILE_TOO_LARGE,
            status_code=413,
            details={"file_size": file_size, "max_size": max_size}
        )


class InvalidFileTypeError(DeepServiceError):
    """无效文件类型异常"""
    
    def __init__(self, file_type: str, allowed_types: list):
        message = f"Invalid file type: {file_type}. Allowed types: {allowed_types}"
        super().__init__(
            message=message,
            error_code=ErrorCodes.INVALID_FILE_TYPE,
            status_code=400,
            details={"file_type": file_type, "allowed_types": allowed_types}
        )


class ServiceUnavailableError(DeepServiceError):
    """服务不可用异常"""
    
    def __init__(self, service_name: str, message: str = None):
        message = message or f"Service unavailable: {service_name}"
        super().__init__(
            message=message,
            error_code=ErrorCodes.SERVICE_UNAVAILABLE,
            status_code=503,
            details={"service_name": service_name}
        )


class TaskFailedError(DeepServiceError):
    """任务失败异常"""
    
    def __init__(self, task_id: str, reason: str = None):
        message = f"Task failed: {task_id}"
        if reason:
            message += f" - {reason}"
        super().__init__(
            message=message,
            error_code=ErrorCodes.TASK_FAILED,
            status_code=500,
            details={"task_id": task_id, "reason": reason}
        )


def create_error_response(
    error_code: str,
    message: str,
    status_code: int = 500,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建标准错误响应"""
    
    response = {
        "success": False,
        "error": {
            "code": error_code,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
        }
    }
    
    if details:
        response["error"]["details"] = details
    
    if request_id:
        response["error"]["request_id"] = request_id
    
    return response


async def deep_service_exception_handler(request: Request, exc: DeepServiceError) -> JSONResponse:
    """Deep服务异常处理器"""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.error(
        f"DeepServiceError: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "request_id": request_id,
        }
    )
    
    response_data = create_error_response(
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    
    request_id = getattr(request.state, "request_id", None)
    
    # 映射HTTP状态码到错误代码
    error_code_mapping = {
        400: ErrorCodes.INVALID_REQUEST,
        404: ErrorCodes.NOT_FOUND,
        503: ErrorCodes.SERVICE_UNAVAILABLE,
    }
    
    error_code = error_code_mapping.get(exc.status_code, ErrorCodes.INTERNAL_ERROR)
    
    logger.warning(
        f"HTTPException: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "request_id": request_id,
        }
    )
    
    response_data = create_error_response(
        error_code=error_code,
        message=str(exc.detail),
        status_code=exc.status_code,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理器"""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.warning(
        f"ValidationError: {exc.errors()}",
        extra={
            "errors": exc.errors(),
            "request_id": request_id,
        }
    )
    
    response_data = create_error_response(
        error_code=ErrorCodes.INVALID_REQUEST,
        message="Request validation failed",
        status_code=422,
        details={"validation_errors": exc.errors()},
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=422,
        content=response_data
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.error(
        f"Unhandled exception: {type(exc).__name__} - {str(exc)}",
        extra={
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc(),
            "request_id": request_id,
        }
    )
    
    # 在生产环境中隐藏详细错误信息
    import os
    debug_mode = os.getenv("DEBUG", "false").lower() == "true"
    
    if debug_mode:
        message = f"{type(exc).__name__}: {str(exc)}"
        details = {"traceback": traceback.format_exc()}
    else:
        message = "Internal server error"
        details = None
    
    response_data = create_error_response(
        error_code=ErrorCodes.INTERNAL_ERROR,
        message=message,
        status_code=500,
        details=details,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=500,
        content=response_data
    )


def setup_error_handlers(app):
    """设置错误处理器"""
    
    # 注册异常处理器
    app.add_exception_handler(DeepServiceError, deep_service_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Error handlers registered successfully")
