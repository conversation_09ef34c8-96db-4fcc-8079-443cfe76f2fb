"""
文件管理器
负责文件的保存、验证和基本信息管理
基于原向量化服务的FileProcessor实现
"""

import hashlib
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Tuple, List

from shared.utils.logger import get_logger
from shared.models.file_info import FileInfo, FileStatus
from services.deep_service.config import config

logger = get_logger(__name__)


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.upload_dir = Path(config.upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"文件管理器初始化完成，上传目录: {self.upload_dir}")
    
    def save_uploaded_file(
        self, file_data: bytes, filename: str
    ) -> Tuple[str, FileInfo]:
        """
        保存上传的文件
        基于原向量化服务的实现
        
        Args:
            file_data: 文件数据
            filename: 文件名
            
        Returns:
            (文件路径, 文件信息)
        """
        # 检查文件扩展名
        file_path = Path(filename)
        if file_path.suffix.lower() not in config.allowed_extensions:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
        
        # 检查文件大小
        file_size = len(file_data)
        max_size_bytes = config.max_file_size * 1024 * 1024  # MB to bytes
        if file_size > max_size_bytes:
            raise ValueError(
                f"File too large: {file_size} bytes > {max_size_bytes} bytes"
            )
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        content_hash = hashlib.md5(file_data).hexdigest()[:8]
        safe_filename = f"{file_path.stem}_{timestamp}_{content_hash}{file_path.suffix}"
        
        # 保存文件
        saved_path = self.upload_dir / safe_filename
        with open(saved_path, "wb") as f:
            f.write(file_data)
        
        # 生成文件编码
        file_code = f"{file_path.stem}_{timestamp}_{content_hash}"
        
        # 创建文件信息
        file_info = FileInfo(
            file_code=file_code,
            file_name=filename,
            file_path=str(saved_path),
            file_size=file_size,
            file_type=file_path.suffix.lower(),
            content_hash=content_hash,
            status=FileStatus.UPLOADING,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        
        logger.info(f"File saved: {filename} -> {safe_filename} (code: {file_code})")
        return str(saved_path), file_info
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否有效
        """
        try:
            file_path = Path(file_path)
            
            # 检查文件是否存在
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            # 检查文件扩展名
            if file_path.suffix.lower() not in config.allowed_extensions:
                logger.error(f"不支持的文件类型: {file_path.suffix}")
                return False
            
            # 尝试读取文件内容进行基本验证
            if file_path.suffix.lower() == ".csv":
                df = pd.read_csv(file_path, nrows=1)  # 只读取第一行进行验证
            elif file_path.suffix.lower() in [".xlsx", ".xls"]:
                df = pd.read_excel(file_path, nrows=1)  # 只读取第一行进行验证
            else:
                return False
            
            # 检查是否有数据
            if df.empty:
                logger.error(f"文件为空: {file_path}")
                return False
            
            logger.debug(f"文件验证通过: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败 {file_path}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件基本信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {"error": "File not found"}
            
            # 基本文件信息
            info = {
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "file_type": file_path.suffix.lower(),
                "created_at": datetime.fromtimestamp(file_path.stat().st_ctime),
                "modified_at": datetime.fromtimestamp(file_path.stat().st_mtime),
            }
            
            # 尝试获取数据文件的详细信息
            try:
                if file_path.suffix.lower() == ".csv":
                    df = pd.read_csv(file_path)
                elif file_path.suffix.lower() in [".xlsx", ".xls"]:
                    df = pd.read_excel(file_path)
                else:
                    return info
                
                info.update({
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist(),
                })
                
            except Exception as e:
                logger.warning(f"无法读取文件详细信息 {file_path}: {e}")
                info["read_error"] = str(e)
            
            return info
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return {"error": str(e)}
    
    def cleanup_old_files(self, days: int = 7):
        """
        清理旧文件
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            cleaned_count = 0
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                            logger.debug(f"清理旧文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"清理文件失败 {file_path}: {e}")
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个旧文件")
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
    
    def get_upload_stats(self) -> dict:
        """
        获取上传统计信息
        
        Returns:
            统计信息字典
        """
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "file_types": {},
                "upload_dir": str(self.upload_dir),
            }
            
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    stats["total_files"] += 1
                    file_size = file_path.stat().st_size
                    stats["total_size"] += file_size
                    
                    file_type = file_path.suffix.lower()
                    if file_type in stats["file_types"]:
                        stats["file_types"][file_type]["count"] += 1
                        stats["file_types"][file_type]["size"] += file_size
                    else:
                        stats["file_types"][file_type] = {
                            "count": 1,
                            "size": file_size
                        }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取上传统计失败: {e}")
            return {"error": str(e)}
