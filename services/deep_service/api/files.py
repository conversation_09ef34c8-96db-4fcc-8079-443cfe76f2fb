"""
文件管理API路由
提供文件列表查询和管理功能
"""

from fastapi import APIRouter, HTTPException
from typing import Optional

from shared.utils.logger import get_logger
from services.deep_service.models.responses import FileListResponse
from services.deep_service.core.chroma_client import ChromaClient
from services.deep_service.core.file_manager import FileManager

logger = get_logger(__name__)

router = APIRouter(prefix="/files", tags=["files"])

# 全局实例
chroma_client = ChromaClient()
file_manager = FileManager()


@router.get("/", response_model=FileListResponse)
async def get_file_list(
    limit: Optional[int] = 100,
    offset: Optional[int] = 0,
    include_stats: bool = False
):
    """
    获取文件列表
    保持与原分析服务API的兼容性
    
    Args:
        limit: 返回数量限制
        offset: 偏移量
        include_stats: 是否包含统计信息
        
    Returns:
        文件列表响应
    """
    try:
        logger.info(f"获取文件列表: limit={limit}, offset={offset}")
        
        # 获取所有文件
        all_files = await chroma_client.get_all_files()
        
        # 应用分页
        total = len(all_files)
        files = all_files[offset:offset + limit] if limit else all_files[offset:]
        
        # 添加统计信息
        if include_stats:
            for file_info in files:
                file_info["stats"] = {
                    "document_count": file_info.get("document_count", 0),
                    "collection_name": file_info.get("collection_name"),
                }
        
        return FileListResponse(
            success=True,
            message=f"File list retrieved: {len(files)} files",
            files=files,
            total=total,
        )
        
    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"File list query failed: {str(e)}")


@router.get("/{file_code}")
async def get_file_details(file_code: str):
    """
    获取文件详细信息
    
    Args:
        file_code: 文件编码
        
    Returns:
        文件详细信息
    """
    try:
        logger.info(f"获取文件详细信息: {file_code}")
        
        # 从ChromaDB获取文件信息
        file_info = await chroma_client.get_file_info(file_code)
        
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail=f"File {file_code} not found"
            )
        
        return {
            "success": True,
            "message": "File details retrieved",
            "file_info": file_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件详细信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"File details query failed: {str(e)}")


@router.get("/{file_code}/documents")
async def get_file_documents(
    file_code: str,
    limit: Optional[int] = 10,
    query: Optional[str] = None
):
    """
    获取文件的文档内容
    
    Args:
        file_code: 文件编码
        limit: 返回数量限制
        query: 查询文本（用于相似性搜索）
        
    Returns:
        文档列表
    """
    try:
        logger.info(f"获取文件文档: {file_code}, query={query}")
        
        if query:
            # 执行相似性搜索
            documents = await chroma_client.query_documents(
                file_code=file_code,
                query_text=query,
                n_results=limit
            )
        else:
            # 获取基本文档信息
            file_info = await chroma_client.get_file_info(file_code)
            if not file_info:
                raise HTTPException(
                    status_code=404,
                    detail=f"File {file_code} not found"
                )
            
            documents = [{
                "message": "Document content retrieval without query not fully implemented",
                "document_count": file_info.get("document_count", 0)
            }]
        
        return {
            "success": True,
            "message": f"Documents retrieved: {len(documents)} documents",
            "file_code": file_code,
            "documents": documents,
            "query": query
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"Documents query failed: {str(e)}")


@router.get("/stats/summary")
async def get_files_summary():
    """
    获取文件统计摘要
    
    Returns:
        文件统计摘要
    """
    try:
        logger.info("获取文件统计摘要")
        
        # 获取所有文件
        all_files = await chroma_client.get_all_files()
        
        # 计算统计信息
        total_files = len(all_files)
        total_documents = sum(f.get("document_count", 0) for f in all_files)
        
        # 按文件类型分组（如果有元数据的话）
        file_types = {}
        for file_info in all_files:
            # 从file_code中推断文件类型（简单实现）
            file_code = file_info.get("file_code", "")
            if "_" in file_code:
                # 假设file_code格式为 "filename_timestamp_hash"
                filename_part = file_code.split("_")[0]
                # 这里可以进一步推断文件类型，暂时使用简单分类
                file_type = "unknown"
                if any(keyword in filename_part.lower() for keyword in ["risk", "风险"]):
                    file_type = "risk_data"
                elif any(keyword in filename_part.lower() for keyword in ["user", "用户"]):
                    file_type = "user_data"
                else:
                    file_type = "general"
                
                if file_type in file_types:
                    file_types[file_type]["count"] += 1
                    file_types[file_type]["documents"] += file_info.get("document_count", 0)
                else:
                    file_types[file_type] = {
                        "count": 1,
                        "documents": file_info.get("document_count", 0)
                    }
        
        # 获取上传统计
        upload_stats = file_manager.get_upload_stats()
        
        summary = {
            "total_files": total_files,
            "total_documents": total_documents,
            "file_types": file_types,
            "upload_stats": upload_stats,
            "recent_files": all_files[:5] if all_files else [],  # 最近5个文件
        }
        
        return {
            "success": True,
            "message": "Files summary retrieved",
            "summary": summary
        }
        
    except Exception as e:
        logger.error(f"获取文件统计摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"Files summary query failed: {str(e)}")


@router.post("/cleanup")
async def cleanup_old_files(days: int = 7):
    """
    清理旧文件
    
    Args:
        days: 保留天数
        
    Returns:
        清理结果
    """
    try:
        logger.info(f"清理旧文件: 保留 {days} 天")
        
        # 清理本地上传文件
        file_manager.cleanup_old_files(days)
        
        return {
            "success": True,
            "message": f"Old files cleanup completed (keeping files from last {days} days)",
            "retention_days": days
        }
        
    except Exception as e:
        logger.error(f"清理旧文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"Cleanup failed: {str(e)}")
