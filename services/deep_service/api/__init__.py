"""
Deep服务API路由
合并了原有分析服务和向量化服务的API
"""

from .health import router as health_router
from .upload import router as upload_router
from .analyze import router as analyze_router
from .status import router as status_router
from .files import router as files_router
from .tasks import router as tasks_router
from .results import router as results_router

__all__ = [
    "health_router",
    "upload_router",
    "analyze_router",
    "status_router",
    "files_router",
    "tasks_router",
    "results_router",
]
