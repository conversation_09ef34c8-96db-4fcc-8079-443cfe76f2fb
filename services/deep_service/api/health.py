"""
健康检查API路由
合并了原有分析服务和向量化服务的健康检查
"""

from fastapi import APIRouter
from services.deep_service.models.responses import HealthResponse
from services.deep_service.core.health_checker import HealthChecker
from shared.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(tags=["health"])

# 全局健康检查器实例
health_checker = HealthChecker()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    统一健康检查接口
    检查所有组件的健康状态：Redis、ChromaDB、LLM服务等

    Returns:
        健康状态
    """
    try:
        # 执行各组件健康检查
        checks = await health_checker.check_all_components()

        # 判断整体健康状态
        all_healthy = all(checks.values())
        status = "healthy" if all_healthy else "unhealthy"

        return HealthResponse(
            status=status,
            service="deep-service",
            version="1.0.0",
            checks=checks
        )

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return HealthResponse(
            status="unhealthy",
            service="deep-service",
            version="1.0.0",
            checks={"error": False},
        )


@router.get("/")
async def root():
    """
    根路径
    返回统一服务信息

    Returns:
        服务信息
    """
    return {
        "service": "deep-service",
        "version": "1.0.0",
        "description": "Deep Risk RAG Deep Service - 统一的文件上传、向量化和风险分析服务",
        "endpoints": {
            # 文件上传相关 (原向量化服务)
            "upload": "/upload/",
            "upload_status": "/status/{file_code}",
            
            # 风险分析相关 (原分析服务)
            "analyze": "/analyze/{file_code}",
            "analysis_result": "/result/{analysis_id}",
            "quick_analysis": "/analyze/{file_code}/quick",
            
            # 文件管理
            "files": "/files/",
            
            # 任务管理 (新增)
            "task_status": "/tasks/{task_id}",
            
            # 系统相关
            "health": "/health",
            "docs": "/docs",
        },
        "features": [
            "异步文件上传和向量化",
            "基于Celery的任务队列",
            "风险分析和预测",
            "多种LLM支持",
            "实时任务状态跟踪",
        ]
    }
