"""
分析结果查询API路由
保持与原分析服务的完全兼容性
"""

from fastapi import APIRouter, HTTPException
from typing import Optional

from shared.utils.logger import get_logger
from services.deep_service.models.responses import AnalysisResultResponse
from services.deep_service.core.task_manager import TaskManager
from shared.celery_config import TaskStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/result", tags=["results"])

# 全局实例
task_manager = TaskManager()


@router.get("/{analysis_id}", response_model=AnalysisResultResponse)
async def get_analysis_result(analysis_id: str, wait_for_result: bool = False):
    """
    获取分析结果
    保持与原分析服务API的完全兼容性
    
    Args:
        analysis_id: 分析ID（实际上是Celery任务ID）
        wait_for_result: 是否等待结果完成
        
    Returns:
        分析结果响应
    """
    try:
        logger.info(f"获取分析结果: {analysis_id}")
        
        # 获取任务状态
        task_status = await task_manager.get_task_status(analysis_id)
        
        if task_status.get("status") == TaskStatus.SUCCESS:
            # 任务成功完成，返回结果
            result = task_status.get("result")
            
            if result:
                return AnalysisResultResponse(
                    success=True,
                    message="Analysis result retrieved successfully",
                    analysis_result=result
                )
            else:
                return AnalysisResultResponse(
                    success=False,
                    message="Analysis completed but no result found"
                )
                
        elif task_status.get("status") == TaskStatus.FAILURE:
            # 任务失败
            error_msg = task_status.get("error", "Unknown error")
            return AnalysisResultResponse(
                success=False,
                message=f"Analysis failed: {error_msg}"
            )
            
        elif task_status.get("status") in [TaskStatus.PENDING, TaskStatus.STARTED]:
            # 任务还在进行中
            if wait_for_result:
                # 如果要求等待结果，可以实现轮询逻辑
                # 这里暂时返回进行中状态
                pass
            
            return AnalysisResultResponse(
                success=False,
                message=f"Analysis is still in progress (status: {task_status.get('status')})"
            )
            
        else:
            # 未知状态
            return AnalysisResultResponse(
                success=False,
                message=f"Unknown analysis status: {task_status.get('status')}"
            )
            
    except Exception as e:
        logger.error(f"获取分析结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"Result query failed: {str(e)}")


@router.get("/{analysis_id}/status")
async def get_analysis_status(analysis_id: str):
    """
    获取分析状态（不包含完整结果）
    
    Args:
        analysis_id: 分析ID
        
    Returns:
        分析状态信息
    """
    try:
        logger.info(f"获取分析状态: {analysis_id}")
        
        # 获取任务状态
        task_status = await task_manager.get_task_status(analysis_id)
        
        return {
            "success": True,
            "message": "Analysis status retrieved",
            "analysis_id": analysis_id,
            "status": task_status.get("status"),
            "progress": task_status.get("progress"),
            "error": task_status.get("error"),
            "created_at": task_status.get("created_at"),
            "started_at": task_status.get("started_at"),
            "completed_at": task_status.get("completed_at"),
        }
        
    except Exception as e:
        logger.error(f"获取分析状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Status query failed: {str(e)}")


@router.get("/{analysis_id}/progress")
async def get_analysis_progress(analysis_id: str):
    """
    获取分析进度
    
    Args:
        analysis_id: 分析ID
        
    Returns:
        分析进度信息
    """
    try:
        logger.info(f"获取分析进度: {analysis_id}")
        
        # 获取任务状态
        task_status = await task_manager.get_task_status(analysis_id)
        
        progress_info = {
            "analysis_id": analysis_id,
            "status": task_status.get("status"),
            "progress": task_status.get("progress", {}),
        }
        
        # 根据状态计算进度百分比
        status = task_status.get("status")
        if status == TaskStatus.PENDING:
            progress_info["percentage"] = 0
            progress_info["stage"] = "等待开始"
        elif status == TaskStatus.STARTED:
            progress_info["percentage"] = 50
            progress_info["stage"] = "分析中"
        elif status == TaskStatus.SUCCESS:
            progress_info["percentage"] = 100
            progress_info["stage"] = "完成"
        elif status == TaskStatus.FAILURE:
            progress_info["percentage"] = 0
            progress_info["stage"] = "失败"
        else:
            progress_info["percentage"] = 0
            progress_info["stage"] = "未知"
        
        return {
            "success": True,
            "message": "Analysis progress retrieved",
            "progress": progress_info
        }
        
    except Exception as e:
        logger.error(f"获取分析进度失败: {e}")
        raise HTTPException(status_code=500, detail=f"Progress query failed: {str(e)}")


@router.delete("/{analysis_id}")
async def delete_analysis_result(analysis_id: str):
    """
    删除分析结果
    
    Args:
        analysis_id: 分析ID
        
    Returns:
        删除结果
    """
    try:
        logger.info(f"删除分析结果: {analysis_id}")
        
        # 取消任务（如果还在运行）
        success = await task_manager.cancel_task(analysis_id)
        
        return {
            "success": success,
            "message": f"Analysis {analysis_id} {'cancelled' if success else 'cancellation failed'}",
            "analysis_id": analysis_id
        }
        
    except Exception as e:
        logger.error(f"删除分析结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"Delete failed: {str(e)}")


@router.get("/batch/{analysis_ids}")
async def get_batch_analysis_results(analysis_ids: str):
    """
    批量获取分析结果
    
    Args:
        analysis_ids: 逗号分隔的分析ID列表
        
    Returns:
        批量分析结果
    """
    try:
        analysis_id_list = [aid.strip() for aid in analysis_ids.split(",")]
        logger.info(f"批量获取分析结果: {len(analysis_id_list)} 个分析")
        
        results = []
        
        for analysis_id in analysis_id_list:
            try:
                task_status = await task_manager.get_task_status(analysis_id)
                
                result_info = {
                    "analysis_id": analysis_id,
                    "status": task_status.get("status"),
                    "success": task_status.get("status") == TaskStatus.SUCCESS,
                }
                
                if task_status.get("status") == TaskStatus.SUCCESS:
                    result_info["result"] = task_status.get("result")
                elif task_status.get("status") == TaskStatus.FAILURE:
                    result_info["error"] = task_status.get("error")
                
                results.append(result_info)
                
            except Exception as e:
                logger.error(f"获取分析结果失败 {analysis_id}: {e}")
                results.append({
                    "analysis_id": analysis_id,
                    "status": "ERROR",
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Batch analysis results retrieved: {len(results)} analyses",
            "results": results,
            "total_analyses": len(analysis_id_list),
            "completed_analyses": len([r for r in results if r.get("success")])
        }
        
    except Exception as e:
        logger.error(f"批量获取分析结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"Batch results query failed: {str(e)}")
