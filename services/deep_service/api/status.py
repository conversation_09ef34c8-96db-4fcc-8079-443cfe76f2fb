"""
状态查询API路由
提供文件处理状态和任务状态查询
"""

from fastapi import APIRouter, HTTPException
from typing import Optional

from shared.utils.logger import get_logger
from services.deep_service.models.responses import StatusResponse
from services.deep_service.core.task_manager import TaskManager
from services.deep_service.core.chroma_client import ChromaClient
from shared.models.file_info import FileStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/status", tags=["status"])

# 全局实例
task_manager = TaskManager()
chroma_client = ChromaClient()


@router.get("/{file_code}", response_model=StatusResponse)
async def get_file_status(file_code: str, include_details: bool = False):
    """
    获取文件处理状态
    保持与原向量化服务API的兼容性
    
    Args:
        file_code: 文件编码
        include_details: 是否包含详细信息
        
    Returns:
        文件状态响应
    """
    try:
        logger.info(f"查询文件状态: {file_code}")
        
        # 检查文件是否存在于向量数据库中
        file_info = await chroma_client.get_file_info(file_code)
        
        if not file_info:
            # 文件不存在于向量数据库中，可能还在处理中或处理失败
            return StatusResponse(
                success=False,
                message="File not found in vector database",
                file_code=file_code,
                status=FileStatus.NOT_FOUND,
            )
        
        # 文件存在，说明向量化已完成
        response_data = {
            "success": True,
            "message": "File processing completed",
            "file_code": file_code,
            "status": FileStatus.COMPLETED,
        }
        
        if include_details:
            response_data.update({
                "document_count": file_info.get("document_count", 0),
                "collection_name": file_info.get("collection_name"),
                "created_at": file_info.get("created_at"),
            })
        
        return StatusResponse(**response_data)
        
    except Exception as e:
        logger.error(f"查询文件状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Status query failed: {str(e)}")


@router.get("/batch/{file_codes}")
async def get_batch_file_status(file_codes: str):
    """
    批量查询文件状态
    
    Args:
        file_codes: 逗号分隔的文件编码列表
        
    Returns:
        批量状态响应
    """
    try:
        file_code_list = [code.strip() for code in file_codes.split(",")]
        logger.info(f"批量查询文件状态: {len(file_code_list)} 个文件")
        
        results = []
        
        for file_code in file_code_list:
            try:
                file_info = await chroma_client.get_file_info(file_code)
                
                if file_info:
                    results.append({
                        "file_code": file_code,
                        "status": FileStatus.COMPLETED,
                        "document_count": file_info.get("document_count", 0),
                        "success": True
                    })
                else:
                    results.append({
                        "file_code": file_code,
                        "status": FileStatus.NOT_FOUND,
                        "success": False,
                        "message": "File not found in vector database"
                    })
                    
            except Exception as e:
                logger.error(f"查询文件状态失败 {file_code}: {e}")
                results.append({
                    "file_code": file_code,
                    "status": FileStatus.FAILED,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Batch status query completed: {len(results)} files",
            "results": results,
            "total_files": len(file_code_list),
            "completed_files": len([r for r in results if r.get("status") == FileStatus.COMPLETED])
        }
        
    except Exception as e:
        logger.error(f"批量查询文件状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"Batch status query failed: {str(e)}")


@router.get("/processing/summary")
async def get_processing_summary():
    """
    获取处理状态摘要
    
    Returns:
        处理状态摘要
    """
    try:
        # 获取队列统计信息
        queue_stats = await task_manager.get_queue_stats()
        
        # 获取所有文件信息
        all_files = await chroma_client.get_all_files()
        
        summary = {
            "total_files": len(all_files),
            "total_documents": sum(f.get("document_count", 0) for f in all_files),
            "queue_stats": queue_stats,
            "recent_files": all_files[:10] if all_files else [],  # 最近10个文件
        }
        
        return {
            "success": True,
            "message": "Processing summary retrieved",
            "summary": summary
        }
        
    except Exception as e:
        logger.error(f"获取处理摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"Summary query failed: {str(e)}")


@router.delete("/{file_code}")
async def delete_file_data(file_code: str):
    """
    删除文件的向量数据
    
    Args:
        file_code: 文件编码
        
    Returns:
        删除结果
    """
    try:
        logger.info(f"删除文件数据: {file_code}")
        
        # 检查文件是否存在
        file_exists = await chroma_client.check_file_exists(file_code)
        if not file_exists:
            raise HTTPException(
                status_code=404,
                detail=f"File {file_code} not found in vector database"
            )
        
        # 删除向量数据
        success = await chroma_client.delete_file(file_code)
        
        if success:
            return {
                "success": True,
                "message": f"File {file_code} deleted successfully",
                "file_code": file_code
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete file {file_code}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"Delete failed: {str(e)}")
