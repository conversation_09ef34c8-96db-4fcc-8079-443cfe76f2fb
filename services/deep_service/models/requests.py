"""
统一服务请求模型
合并了原有分析服务和向量化服务的请求模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel


class UploadRequest(BaseModel):
    """文件上传请求 - 来自向量化服务"""
    force_reprocess: bool = False
    chunk_size: Optional[int] = None
    chunk_overlap: Optional[int] = None


class AnalysisRequest(BaseModel):
    """风险分析请求 - 来自分析服务"""
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1
    callback_url: Optional[str] = None
    enable_streaming: Optional[bool] = None


class BatchAnalysisRequest(BaseModel):
    """批量分析请求 - 来自分析服务"""
    file_codes: List[str]
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1


class StatusRequest(BaseModel):
    """状态查询请求"""
    include_details: bool = False


class VectorizeRequest(BaseModel):
    """向量化请求 - 内部使用"""
    file_code: str
    force_reprocess: bool = False
    chunk_size: Optional[int] = None
    chunk_overlap: Optional[int] = None


class TaskStatusRequest(BaseModel):
    """任务状态查询请求"""
    task_id: str
    include_result: bool = False
