# Worker服务 Dockerfile
# 后台任务处理服务

FROM python:3.10-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY shared/ ./shared/
COPY services/worker_service/ ./services/worker_service/

# 创建必要目录
RUN mkdir -p /app/data/uploads /app/logs

# 设置环境变量
ENV PYTHONPATH=/app

# 启动命令 (默认为worker，可通过docker-compose覆盖)
CMD ["celery", "-A", "services.worker_service.worker", "worker", "--loglevel=info", "--prefetch-multiplier=1"]
