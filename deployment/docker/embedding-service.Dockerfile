# BGE-M3 嵌入服务 Dockerfile
# 支持GPU和CPU环境的多阶段构建

FROM python:3.10-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY services/embedding_service/requirements.txt ./embedding_requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r embedding_requirements.txt

# 复制应用代码
COPY shared/ ./shared/
COPY services/embedding_service/ ./services/embedding_service/
COPY models/ ./models/

# 创建缓存目录
RUN mkdir -p /app/cache

# 设置环境变量
ENV PYTHONPATH=/app
ENV MODEL_CACHE_DIR=/app/cache

# 暴露端口
EXPOSE 8004

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8004/api/v1/health/ready || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "services.embedding_service.main:app", "--host", "0.0.0.0", "--port", "8004"]
