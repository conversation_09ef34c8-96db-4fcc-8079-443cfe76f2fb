# Deep Risk RAG 微服务部署指南

## 概述

本目录包含Deep Risk RAG系统的完整部署配置，支持Docker容器化部署。

## 架构

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Deep Service      │    │  Embedding Service  │    │   Worker Service    │
│   (API Gateway)     │    │   (BGE-M3 GPU)      │    │  (Background Tasks) │
│   Port: 8000        │◄──►│   Port: 8004        │◄──►│   Celery Worker     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
           │                           │                           │
           └───────────────────────────┼───────────────────────────┘
                                       │
           ┌─────────────────────┐    ┌─────────────────────┐
           │      Redis          │    │     ChromaDB        │
           │   (Message Queue)   │    │  (Vector Database)  │
           │   Port: 6379        │    │   Port: 8001        │
           └─────────────────────┘    └─────────────────────┘
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd deep-risk-rag

# 复制环境配置
cp deployment/.env.example deployment/.env

# 编辑环境变量
vim deployment/.env
```

### 2. 启动服务

```bash
# 进入部署目录
cd deployment

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 验证部署

```bash
# 检查服务健康状态
curl http://localhost:8000/health      # Deep服务
curl http://localhost:8004/api/v1/health/ready  # 嵌入服务
curl http://localhost:8001/api/v1/heartbeat     # ChromaDB

# 访问监控界面
open http://localhost:5555  # Flower任务监控
```

## 服务说明

### Deep Service (端口: 8000)
- **功能**: 统一API网关，处理文件上传和分析请求
- **依赖**: Redis, ChromaDB, Embedding Service
- **健康检查**: `GET /health`
- **API文档**: `http://localhost:8000/docs`

### Embedding Service (端口: 8004)
- **功能**: BGE-M3文本嵌入计算
- **资源**: 需要GPU支持（可降级到CPU）
- **健康检查**: `GET /api/v1/health/ready`
- **API文档**: `http://localhost:8004/docs`

### Worker Service
- **功能**: 后台任务处理（向量化、风险分析）
- **依赖**: Redis, ChromaDB, Embedding Service
- **监控**: Flower界面 `http://localhost:5555`

### 基础设施服务

#### Redis (端口: 6379)
- **功能**: 消息队列和缓存
- **持久化**: 启用AOF和RDB
- **配置**: `infrastructure/redis/redis.conf`

#### ChromaDB (端口: 8001)
- **功能**: 向量数据库
- **持久化**: 数据卷挂载
- **配置**: `infrastructure/chromadb/chroma.conf`

## 管理命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f [service_name]

# 进入容器
docker-compose exec [service_name] bash
```

### 数据管理
```bash
# 备份数据
docker-compose exec redis redis-cli BGSAVE
docker run --rm -v deployment_chromadb_data:/data -v $(pwd):/backup alpine tar czf /backup/chromadb_backup.tar.gz -C /data .

# 清理数据
docker-compose down -v  # 删除所有数据卷
```

## 故障排除

### 常见问题

1. **GPU不可用**
   ```bash
   # 检查NVIDIA Docker支持
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   
   # 如果GPU不可用，嵌入服务会自动降级到CPU模式
   ```

2. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs [service_name]
   
   # 检查端口占用
   netstat -tulpn | grep [port]
   ```

3. **内存不足**
   ```bash
   # 调整Docker内存限制
   # 编辑 docker-compose.yml 中的 deploy.resources.limits
   ```

### 性能优化

1. **GPU内存优化**
   - 调整 `GPU_MEMORY_FRACTION` 环境变量
   - 监控GPU使用情况：`nvidia-smi`

2. **Redis优化**
   - 调整 `maxmemory` 配置
   - 选择合适的内存策略

3. **ChromaDB优化**
   - 调整批处理大小
   - 优化索引参数

## 监控和日志

### 日志收集
- 所有服务日志通过Docker日志驱动收集
- 可配置日志轮转和远程日志收集

### 健康监控
- 每个服务都有健康检查端点
- 支持Prometheus指标收集（可选）

### 性能监控
- Flower提供Celery任务监控
- 可集成Grafana仪表板（可选）
