# Deep Risk RAG 微服务环境配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# LLM API 配置
# =============================================================================
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# 服务端口配置
# =============================================================================
# Deep服务端口
DEEP_SERVICE_PORT=8000

# 嵌入服务端口
EMBEDDING_SERVICE_PORT=8004

# ChromaDB端口
CHROMA_PORT=8001

# Redis端口
REDIS_PORT=6379

# Flower监控端口
FLOWER_PORT=5555

# =============================================================================
# 数据库配置
# =============================================================================
# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# ChromaDB配置
CHROMA_HOST=chromadb
CHROMA_PORT=8000
CHROMA_API_VERSION=v1

# =============================================================================
# 服务配置
# =============================================================================
# 日志级别
LOG_LEVEL=INFO

# 调试模式
DEBUG=false

# 文件上传配置
MAX_FILE_SIZE=100
UPLOAD_DIR=./data/uploads

# 任务配置
TASK_TIMEOUT=3600
TASK_RETRY_MAX=3

# =============================================================================
# GPU配置
# =============================================================================
# 设备选择 (auto/cuda/cpu)
DEVICE=auto

# GPU内存分数
GPU_MEMORY_FRACTION=0.8

# =============================================================================
# 监控配置
# =============================================================================
# 启用监控
ENABLE_MONITORING=true

# Prometheus端口
PROMETHEUS_PORT=9090

# Grafana端口
GRAFANA_PORT=3000
